using System;
using System.IO;
using Renci.SshNet;

class SimpleSFTPTest
{
    static void Main(string[] args)
    {
        Console.WriteLine("Testing SFTP Connection...");
        
        string server = "192.168.2.53";
        string username = "hkria";
        string password = "1";
        int port = 22;
        
        try
        {
            // 创建连接信息
            var connectionInfo = new PasswordConnectionInfo(server, port, username, password);
            connectionInfo.Timeout = TimeSpan.FromSeconds(30);
            
            using (var sftp = new SftpClient(connectionInfo))
            {
                Console.WriteLine($"Connecting to {server}:{port} as {username}...");
                sftp.Connect();
                
                if (sftp.IsConnected)
                {
                    Console.WriteLine("Connected successfully!");
                    
                    // 创建测试文件
                    string testFile = "test_upload.txt";
                    File.WriteAllText(testFile, "Hello SFTP Test!");
                    
                    // 上传测试文件
                    using (var fileStream = new FileStream(testFile, FileMode.Open))
                    {
                        Console.WriteLine("Uploading test file...");
                        sftp.UploadFile(fileStream, "/web3/test/test_upload.txt");
                        Console.WriteLine("Upload successful!");
                    }
                    
                    // 清理测试文件
                    File.Delete(testFile);
                    
                    sftp.Disconnect();
                    Console.WriteLine("Test completed successfully!");
                }
                else
                {
                    Console.WriteLine("Failed to connect!");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
            Console.WriteLine($"Details: {ex}");
        }
        
        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
}
