using System;
using System.IO;
using Sony_Song_Import.util;

namespace Sony_Song_Import
{
    class DirectSFTPTest
    {
        static void Main(string[] args)
        {
            Console.WriteLine("Direct SFTP Upload Test...");
            
            // 读取配置文件
            try
            {
                AppCommon.readConfig("sony.xml");
                
                Console.WriteLine($"FTP Server: {AppCommon.FTPServer}");
                Console.WriteLine($"FTP User: {AppCommon.FTPUser}");
                Console.WriteLine($"FTP Password: {AppCommon.FTPPassword}");
                
                // 创建测试文件
                string testFile = "sftp_test.txt";
                File.WriteAllText(testFile, $"SFTP Test File - {DateTime.Now}");
                
                Console.WriteLine($"Created test file: {testFile}");
                
                // 测试上传
                string remotePath = "test/sftp_test.txt";
                Console.WriteLine($"Uploading {testFile} to {remotePath}...");
                
                bool result = Util.uploadFile(testFile, remotePath);
                
                if (result)
                {
                    Console.WriteLine("✅ SFTP Upload Test: SUCCESS");
                }
                else
                {
                    Console.WriteLine("❌ SFTP Upload Test: FAILED");
                }
                
                // 清理测试文件
                if (File.Exists(testFile))
                {
                    File.Delete(testFile);
                    Console.WriteLine("Test file cleaned up.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
