-- 删除Sony音乐导入测试中的特定track记录
-- 基于日志分析获得的track_id列表

-- 删除track_id: 633034, 4250186-4250197

-- 删除关联表记录
DELETE FROM `avs`.`label_link` WHERE `track_id` IN (633034, 4250186, 4250187, 4250188, 4250189, 4250190, 4250191, 4250192, 4250193, 4250194, 4250195, 4250196, 4250197);

DELETE FROM `avs`.`m3_album_link` WHERE `track_id` IN (633034, 4250186, 4250187, 4250188, 4250189, 4250190, 4250191, 4250192, 4250193, 4250194, 4250195, 4250196, 4250197);

DELETE FROM `avs`.`m3_artist_link` WHERE `track_id` IN (633034, 4250186, 4250187, 4250188, 4250189, 4250190, 4250191, 4250192, 4250193, 4250194, 4250195, 4250196, 4250197);

DELETE FROM `avs`.`track_name` WHERE `track_id` IN (633034, 4250186, 4250187, 4250188, 4250189, 4250190, 4250191, 4250192, 4250193, 4250194, 4250195, 4250196, 4250197);

-- 最后删除主表记录
DELETE FROM `avs`.`track` WHERE `track_id` IN (633034, 4250186, 4250187, 4250188, 4250189, 4250190, 4250191, 4250192, 4250193, 4250194, 4250195, 4250196, 4250197);

-- 查询确认删除结果
SELECT COUNT(*) as remaining_tracks FROM `avs`.`track` WHERE `track_id` IN (633034, 4250186, 4250187, 4250188, 4250189, 4250190, 4250191, 4250192, 4250193, 4250194, 4250195, 4250196, 4250197);
