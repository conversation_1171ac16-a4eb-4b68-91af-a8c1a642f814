#!/bin/bash

echo "=== Sony Track Deletion Script ==="
echo "Deleting tracks: 4250198-4250210"
echo

# 数据库连接信息
HOST="*************"
PORT="3336"
USER="avs"
PASSWORD="NCPxzZafXFRtPY4t"
DATABASE="avs"

# 要删除的track_id列表（最新插入的记录）
TRACK_IDS="4250198,4250199,4250200,4250201,4250202,4250203,4250204,4250205,4250206,4250207,4250208,4250209,4250210"

echo "🔌 Connecting to database $HOST:$PORT/$DATABASE..."

# 执行删除操作
mysql -h "$HOST" -P "$PORT" -u "$USER" -p"$PASSWORD" "$DATABASE" << EOF

-- 删除关联表记录
DELETE FROM \`avs\`.\`label_link\` WHERE \`track_id\` IN ($TRACK_IDS);
DELETE FROM \`avs\`.\`m3_album_link\` WHERE \`track_id\` IN ($TRACK_IDS);
DELETE FROM \`avs\`.\`m3_artist_link\` WHERE \`track_id\` IN ($TRACK_IDS);
DELETE FROM \`avs\`.\`track_name\` WHERE \`track_id\` IN ($TRACK_IDS);

-- 删除主表记录
DELETE FROM \`avs\`.\`track\` WHERE \`track_id\` IN ($TRACK_IDS);

-- 验证删除结果
SELECT COUNT(*) as remaining_tracks FROM \`avs\`.\`track\` WHERE \`track_id\` IN ($TRACK_IDS);

EOF

if [ $? -eq 0 ]; then
    echo "✅ Database deletion completed successfully!"
    echo "🚀 Ready for SFTP upload test..."
else
    echo "❌ Database deletion failed!"
    exit 1
fi
