#!/bin/bash

echo "=== Sony Track Deletion Script ==="
echo "用于删除数据库中的track记录以便重新测试SFTP上传功能"
echo "使用前请先从日志中获取需要删除的track_id列表："
echo "grep 'Track Inserted' logs/20240829/log.txt | sed 's/.*Track Inserted : \([0-9]*\).*/\1/' | sort -n"
echo
echo "当前配置删除的track_id: > 4250000"
echo

# 数据库连接信息
HOST="*************"
PORT="3336"
USER="avs"
PASSWORD="NCPxzZafXFRtPY4t"
DATABASE="avs"

# 使用范围删除，删除所有大于4250000的track_id
TRACK_CONDITION="> 4250000"

echo "🔌 Connecting to database $HOST:$PORT/$DATABASE..."

# 执行删除操作
mysql -h "$HOST" -P "$PORT" -u "$USER" -p"$PASSWORD" "$DATABASE" << EOF

-- 删除关联表记录
DELETE FROM \`avs\`.\`label_link\` WHERE \`track_id\` $TRACK_CONDITION;
DELETE FROM \`avs\`.\`m3_album_link\` WHERE \`track_id\` $TRACK_CONDITION;
DELETE FROM \`avs\`.\`m3_artist_link\` WHERE \`track_id\` $TRACK_CONDITION;
DELETE FROM \`avs\`.\`track_name\` WHERE \`track_id\` $TRACK_CONDITION;

-- 删除主表记录
DELETE FROM \`avs\`.\`track\` WHERE \`track_id\` $TRACK_CONDITION;

-- 验证删除结果
SELECT COUNT(*) as remaining_tracks FROM \`avs\`.\`track\` WHERE \`track_id\` $TRACK_CONDITION;

EOF

if [ $? -eq 0 ]; then
    echo "✅ Database deletion completed successfully!"
    echo "🚀 Ready for SFTP upload test..."
    echo
    echo "下一步："
    echo "1. 删除处理记录: rm logs/20240829/record.dat"
    echo "2. 运行程序: mono \"bin/Debug/Sony Song Import.exe\" 20240829"
    echo "3. 观察日志中的 'Track Inserted' 和 'SFTP: File uploaded successfully'"
else
    echo "❌ Database deletion failed!"
    exit 1
fi
