﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.IO;
using Newtonsoft.Json;
using System.Threading.Tasks;
using System.Xml;
using Sony_Song_Import.Class;
using Sony_Song_Import.util;
using System.Net.Mail;
using System.Diagnostics;

namespace Sony_Song_Import
{
    class SonyImport
    {
        // TODO , read from config
        private static string config = "sony.xml";

        public static string FOLDER_LOCATION;
        public static string LOG_LOCATION;
        public static string NAS_DIR;

        private static string folder_path;
        private static string log_path;
        // the log text
        public static string log_text_file;
        private static string log_text_file_name = "log.txt";
        // the record , which dirs have been done
        public static string log_record_file;
        private static string log_record_file_name = "record.dat";

        // the record array
        // 0 : success
        // 1 : failure
        // 2 : update
        // 3 : missingXML
        private static Dictionary<string, List<string>> record;

        // hard code with thread pool size 5
        private static int poolSize = 5;
        private static bool needEmail = true;
        private static bool forceUpdate = false;

        // the music folder dir list
        private static List<string> dirList;

        // stroing the role array
        public static Dictionary<long, String> role;
        // stroing the label array
        public static Dictionary<long, String> label;

        private static List<string> inserted = new List<string>();
        private static string date;

        static void Main(string[] args)
        {
            try
            {
                
                if (args.Length != 1)
                {
                    Console.WriteLine("Parameters Format Wrong!You should pass a YYYYMMDD as a parameter.");
                    return;
                }

                Console.WriteLine("This is a program which automatically reads and uploads Sony Files to HKRIA M3.");
                DateTime current = DateTime.Now;
                Console.WriteLine("All Right Reserverd@" + current.Year);

                readConfig();

                date = args[0];
                folder_path = FOLDER_LOCATION + date + "/";
                log_path = LOG_LOCATION + date + "/";

                // Exit Program if The Sony folder with the date does not exist
                if (!Directory.Exists(folder_path))
                {
                    throw new Exception("Directory Not Exists. Program Exits.");
                }

                // Create Log Folder if not exist
                if (!Directory.Exists(log_path))
                {
                    Directory.CreateDirectory(log_path);
                }

                log_text_file = log_path + log_text_file_name;
                log_record_file = log_path + log_record_file_name;
                
                init();

                startImport();

                sendEmail();

            }
            catch (Exception ex)
            {
                Util.writeToWholeLog("Main Error: " + ex.Message , true);
            }
            finally
            {
                endImport();
            }
        }

        /*
         * Read the config xml
         * */
        private static void readConfig()
        {
            if (!File.Exists(config))
            {
                Console.WriteLine("Config XML Missing.");
                return;
            }

            XmlDocument doc = new XmlDocument();
            doc.Load(config);

            XmlNode _con = doc.SelectSingleNode("sonyconfig");

            if (_con == null)
            {
                return;
            }

            // for checking the params are not null nor empty
            List<string> check = new List<string>();

            XmlNode setting = _con.SelectSingleNode("app_setting");
            if (setting != null){
                poolSize = setting.getInt("thread", 5);
                needEmail = setting.getText("need_email").ToLower() == "true";
                forceUpdate = setting.getText("force_update").ToLower() == "true";
            }

            XmlNode dbinfo = _con.SelectSingleNode("dbinfo");
            if (dbinfo != null)
            {
                AppCommon.SQLServer = dbinfo.getText("host");
                AppCommon.SQLPort = dbinfo.getInt("port", 3306); // 默认3306
                AppCommon.SQLUser = dbinfo.getText("user");
                AppCommon.SQLPassword = dbinfo.getText("password");
                AppCommon.SQLDB = dbinfo.getText("database");

                check.Add(AppCommon.SQLServer);
                check.Add(AppCommon.SQLUser);
                check.Add(AppCommon.SQLPassword);
                check.Add(AppCommon.SQLDB);
            }
            else
            {
                return;
            }

            XmlNode ftpinfo = _con.SelectSingleNode("ftpinfo");
            if (ftpinfo != null)
            {
                AppCommon.FTPServer = ftpinfo.getText("host");
                AppCommon.FTPUser = ftpinfo.getText("user");
                AppCommon.FTPPassword = ftpinfo.getText("password");
                AppCommon.UploadMethod = ftpinfo.getText("upload_method", "ftp");

                check.Add(AppCommon.FTPServer);
                check.Add(AppCommon.FTPUser);
                check.Add(AppCommon.FTPPassword);
            }
            else
            {
                return;
            }

            XmlNode source = _con.SelectSingleNode("source");
            if (source != null)
            {
                FOLDER_LOCATION = source.getText("root_folder");
                LOG_LOCATION = source.getText("log_folder");
                NAS_DIR = source.getText("nas_dir");

                check.Add(FOLDER_LOCATION);
                check.Add(LOG_LOCATION);
                check.Add(NAS_DIR);
            }
            else
            {
                return;
            }

            XmlNode email = _con.SelectSingleNode("notice");
            if (email != null)
            {
                AppCommon.SMTPHost = email.getText("mail_host");
                AppCommon.SMTPAdd = email.getText("mail_address");

                check.Add(AppCommon.SMTPHost);
                check.Add(AppCommon.SMTPAdd);
            }
            else
            {
                return;
            }

            // check if any are nullo or empty , ie , missing
            if (!check.All(s=> !string.IsNullOrEmpty(s))){
                Console.WriteLine("Some info is missing in Configuration XML.");
                return;
            }

            foreach (XmlNode _t in email.SelectNodes("to/staff"))
            {
                string mail = _t.getText();
                if (string.IsNullOrEmpty(mail))
                {
                    continue;
                }

                AppCommon.SMTPTo.Add(mail);
            }

        }

        /*
         * Read the record.dat or Init it 
        */
        private static void init()
        {
            bool record_inited = false;
            if (File.Exists(log_record_file))
            {
                using (StreamReader r = new StreamReader(log_record_file))
                {
                    string json = r.ReadToEnd();

                    record = JsonConvert.DeserializeObject<Dictionary<string, List<string>>>(json);

                    if (record != null)
                    {

                        if (record.Count == 3)
                        {
                            // old data , that has no MissingXML field , add it
                            record.Add("missingXML", new List<string>());
                        }
                        record_inited = true;
                    }
                }
            }

            if (!record_inited)
            {
                record = new Dictionary<string, List<string>>
                {
                    { "success" , new List<string>() },
                    { "failure" , new List<string>() },
                    { "update" , new List<string>() },
                    { "missingXML" , new List<string>() }
                };
            }

            // get the role
            DB.shared.select("select role_id , role from role order by role_id asc" , null , dr =>
            {
                role = new Dictionary<long, string>();
                while (dr.Read())
                {
                    role.Add((int)dr["role_id"], (string)dr["role"]);
                }
            });

            // get the label
            DB.shared.select("select label_id , label_name from label order by label_id asc", null, dr =>
            {
                label = new Dictionary<long, string>();
                while (dr.Read())
                {
                    label.Add((int)dr["label_id"], (string)dr["label_name"]);
                }
            });
        }

        private static void startImport()
        {
            Util.writeToWholeLog("------------ Process Start ------------");

            string[] dirs = Directory.GetDirectories(folder_path);

            if (dirs.Length <= 0)
            {
                throw new Exception("No Directories inside the path is found.");
            }

            // to sort the files in the oreder of time
            Array.Sort(dirs, delegate (string str1, string str2)
            {
               string t1 = str1.Split('_')[1];
               string t2 = str2.Split('_')[1];
               return t1.CompareTo(t2);
            });

            int count = dirs.Length;

            dirList = new List<string>(dirs);

            importParallel(dirList);
        }    

        private static void importParallel(List<string> dirs)
        {
            Parallel.For(
                0,
                dirs.Count,
                new ParallelOptions { MaxDegreeOfParallelism = poolSize },
                (i) =>
                {
                    readXML(dirs[i]);
                }
            );

            Console.WriteLine("should print this when all finish");
        }

        private static void readXML(string path)
        {
            if (!forceUpdate)
            {
                if (record["success"].Contains(path) || record["update"].Contains(path))
                {
                    return;
                }
            }

            var xml = new DirectoryInfo(path).GetFiles("*.xml", SearchOption.TopDirectoryOnly);
            if (xml.Length == 0)
            {
                Util.writeToWholeLog("Missing XML file in " + path, true);
                record["missingXML"].Add(path);
                return;
            }
            else if (xml.Length != 1)
            {
                Util.writeToWholeLog("More than 1 XML file in " + path, true);
                record["missingXML"].Add(path);
                return;
            }

            string xmlPath = xml[0].FullName;

            Util.writeToWholeLog("Going to read xml : " + xmlPath);

            // Start to read the xml
            XmlDocument doc = new XmlDocument();

            try
            {
                doc.Load(xmlPath);
                Util.writeToWholeLog("XML loaded successfully: " + xmlPath);
            }
            catch(Exception ex)
            {
                Util.writeToWholeLog("MalFormat XML file in " + path + ": " + ex.Message, true);
                record["missingXML"].Add(path);
                return;
            }

            try
            {
                // 设置命名空间管理器
                XmlNamespaceManager nsmgr = new XmlNamespaceManager(doc.NameTable);
                nsmgr.AddNamespace("ernm", "http://ddex.net/xml/ern/43");
                Util.writeToWholeLog("Namespace manager created");

                // the message header
                XmlNode header = doc.DocumentElement.SelectSingleNode("MessageHeader");
                Util.writeToWholeLog("MessageHeader found: " + (header != null));

                // update indicator (可能不存在，默认为新消息)
                XmlNode update = doc.DocumentElement.SelectSingleNode("UpdateIndicator");
                string updateText = update?.InnerText ?? "NewReleaseMessage";
                Util.writeToWholeLog("UpdateIndicator: " + updateText);

                // resourse list
                XmlNode res = doc.DocumentElement.SelectSingleNode("ResourceList");
                Util.writeToWholeLog("ResourceList found: " + (res != null));

                Album am = new Album(path , updateText , xmlPath);
                Util.writeToWholeLog("Album object created successfully");

                Util.writeToWholeLog("Starting forceUpdate check");
                if (!forceUpdate)
                {
                    if (am.isAlreadySuccess())
                {
                    Util.writeToWholeLog("Already Success: " + path, true);
                    record["success"].Add(path);
                    return;
                }
                else if (am.isAlreadyUpdate())
                {
                    Util.writeToWholeLog("Already Check Update (ignored): " + path, true);
                    record["update"].Add(path);
                    return;
                    }
                }

                Util.writeToWholeLog("Checking if update");
                if (am.isUpdate)
            {
                am.writeUpdate();
                record["update"].Add(path);
                Util.writeToWholeLog("Updating Album. Ignored.", true);
                Util.writeToWholeLog(string.Empty);
                return;
            }

            // 检查必要的节点是否存在
            Util.writeToWholeLog("Checking ResourceList");
            if (res == null)
            {
                Util.writeToWholeLog("Missing ResourceList in XML: " + xmlPath, true);
                record["missingXML"].Add(path);
                return;
            }

            Util.writeToWholeLog("Getting ReleaseList");
            XmlNode relList = doc.DocumentElement.SelectSingleNode("ReleaseList");
            if (relList == null)
            {
                Util.writeToWholeLog("Missing ReleaseList in XML: " + xmlPath, true);
                record["missingXML"].Add(path);
                return;
            }

            Util.writeToWholeLog("Starting getCover");
            // check and upload the cover
            am.getCover(res);

            Util.writeToWholeLog("Starting getAlbum");
            // get the album info
            am.getAlbum(relList);

            Util.writeToWholeLog("Starting getTracks");
            // get the track info
            am.getTracks(res);

            Util.writeToWholeLog("Finished getTracks, starting displayAlbum");
            // Adding Album and Tracks to Database
            am.displayAlbum();

            Util.writeToWholeLog("Finished displayAlbum");

            if (am.isCompleted && am.success)
            {
                am.writeSuccess();

                record["success"].Add(path);

                if (record["failure"].Contains(path))
                {
                    record["failure"].Remove(path);
                }

                if (record["update"].Contains(path))
                {
                    record["update"].Remove(path);
                }

                inserted.Add(am.getResult());

            }
            else if (am.isUpdate)
            {
                if (!record["update"].Contains(path))
                {
                    record["update"].Add(path);
                }
            }
            else
            {
                // operation fail
                if (!record["failure"].Contains(path))
                {
                    record["failure"].Add(path);
                }
            }
            }
            catch (Exception ex)
            {
                Util.writeToWholeLog("Error in XML processing: " + ex.Message, true);
                record["missingXML"].Add(path);
            }
        }

        private static void endImport()
        {
            Util.writeRecord(record);

            Util.writeToWholeLog("------------ Process End ------------");
            // adding line break
            Util.writeToWholeLog("");

            Console.WriteLine("Log can be read in the log folder.");
            Console.WriteLine("Program finish and exit.");

            // Keep the console window open in debug mode.
            keepConsole();
        }

        private static void sendEmail()
        {
            if (!needEmail)
            {
                return;
            }

            MailMessage mail = new MailMessage();
            mail.From = new MailAddress(AppCommon.SMTPAdd);

            foreach (string s in AppCommon.SMTPTo)
            {
                mail.To.Add(s);
            }
            
            SmtpClient client = new SmtpClient();
            client.Port = 25;
            client.DeliveryMethod = SmtpDeliveryMethod.Network;
            client.UseDefaultCredentials = false;
            client.Host = AppCommon.SMTPHost;
            mail.Subject = "Sony Insert Result on " + date;
            mail.IsBodyHtml = true;

            string body = "Below are the result of sony music insertion in folder: " + folder_path + "<br><br>";
            body += "Success : " + record["success"].Count + "; " ;
            body += "Update : " + record["update"].Count + "; ";
            body += "Failure : " + record["failure"].Count + "; ";
            body += "Missing XML : " + record["missingXML"].Count + "<br><br>";

            body += "Below are the new album inserted: <br><br>";
            int count = 1;
            foreach (string s in inserted)
            {
                body += count++.ToString() + " : " + s + "<br>";
            }

            mail.Body = body;
            client.Send(mail);
        }

        [Conditional("DEBUG")]
        private static void keepConsole()
        {
            // Keep the console window open in debug mode.
            Console.ReadKey();
        }

    }
}
