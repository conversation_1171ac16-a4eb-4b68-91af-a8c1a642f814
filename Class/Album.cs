﻿using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Xml;
using System;
using System.IO;
using Sony_Song_Import.util;
using MySql.Data.MySqlClient;

namespace Sony_Song_Import.Class
{
    class Album
    {
        public bool isCompleted = false;
        public bool isUpdate = false;
        public bool success = true;

        private static Object _lock = new Object();

        public string BASE_PATH;
        private string COVER_PATH;
        private string DB_COVER_PATH;
        public string relType;

        public long album_id = -1;
	    private string msgType = "";
	    private string grid = "";
	    private string icpn = "";

        private string duration = "";
        private string copyright = "";

        private string relDate = "";
        public int year = 0;
        public int month = 0;
        
        // for logging the reading result
        private List<string> result = new List<string>();
        private List<Title> titles = new List<Title>();
        private Title formalTitle = null;

        private List<Track> tracks = new List<Track>();

        private Regex regex = new Regex(@"PT(?<hour>\d+)H(?<minute>\d+)M(?<second>\d+)S");
        private Match match;

        private Dictionary<string, long> dbresult;
        private List<object> _r;

        private string displayArtist;

        private string log_path;
        private string log_success;
        private string log_update;

        public Album(string path , string update ,string xmlPath)
        {
            BASE_PATH = path + '/';
            msgType = update;
            isUpdate = update == "UpdateMessage";
            log_path = SonyImport.LOG_LOCATION +
                Util.dirname(xmlPath).Replace("\\", "/").Replace(SonyImport.FOLDER_LOCATION, string.Empty) + '/';
            log_success = log_path + "success.dat";
            log_update = log_path + "update.dat";

            if (!Directory.Exists(log_path))
            {
                Directory.CreateDirectory(log_path);
            }
        }

        public void getCover(XmlNode res)
        {
            Util.writeToWholeLog("getCover: Starting image processing");
            XmlNodeList covers = res.SelectNodes("Image");
            Util.writeToWholeLog("getCover: Found " + covers.Count + " Image nodes");

            foreach ( XmlNode c in covers )
            {
                string imageType = c.getText("Type");
                Util.writeToWholeLog("getCover: Processing image with Type: " + imageType);

                if (imageType == "FrontCoverImage")
                {
                    Util.writeToWholeLog("getCover: Found FrontCoverImage, looking for File node");

                    // 修正XML路径：Sony XML使用 TechnicalDetails/File 而不是 ImageDetailsByTerritory/TechnicalImageDetails/File
                    XmlNode file = c.SelectSingleNode("TechnicalDetails/File");

                    if (file == null)
                    {
                        Util.writeToWholeLog("getCover: File node not found in TechnicalDetails", true);
                        if (!isUpdate)
                        {
                            // not update and no image , error
                            Util.writeToWholeLog("Missing Image Property in MetaData", true);
                            result.Add("[Error] Missing Image Property in MetaData");
                        }
                        break;
                    }

                    Util.writeToWholeLog("getCover: File node found, checking file existence");
                    Dictionary <string, object> _result = Util.checkSonyFileExist(file, BASE_PATH);

                    string path = (string)_result["path"];
                    Util.writeToWholeLog("getCover: File path: " + path);

                    if ((bool)_result["success"])
                    {
                        COVER_PATH = path;
                        DB_COVER_PATH = path.Replace(SonyImport.FOLDER_LOCATION, SonyImport.NAS_DIR);
                        Util.writeToWholeLog("getCover: Uploading cover from " + path + " to " + DB_COVER_PATH);

                        if (Util.uploadFile(path , DB_COVER_PATH))
                        {
                            Util.writeToWholeLog("Upload Cover Success");
                        }
                        else
                        {
                            Util.writeToWholeLog("Upload Cover Fail", true);
                            success = false;
                        }
                    }
                    else
                    {
                        success = false;
                        string msg = (string)_result["msg"];

                        Util.writeToWholeLog("Cover fail :" + path +
                            " with reason : " + msg , true);

                        result.Add("[ERROR] " + path + " fail because " + msg);
                    }

                }
                else
                {
                    Util.writeToWholeLog("getCover: Skipping image with Type: " + imageType);
                }
            }

            Util.writeToWholeLog("getCover: Completed image processing");
        }

        public void getAlbum(XmlNode rel)
        {
            XmlNodeList releases = rel.SelectNodes("Release");
            foreach ( XmlNode r in releases)
            {
                if (r.getText("ReleaseReference") == "R0")
                {
                    relType = r.getText("ReleaseType");

                    XmlNode releaseID = r.SelectSingleNode("ReleaseId");
                    if (releaseID != null)
                    {
                        grid = releaseID.getText("GRid");
                        icpn = releaseID.getText("ICPN");
                    }
                    else
                    {
                        Util.writeToWholeLog("Missing ReleaseId in XML", true);
                        result.Add("[ERROR] Missing ReleaseId in XML");
                    }

                    // Sony XML 使用 DisplayTitle 而不是 ReferenceTitle
                    XmlNode displayTitle = r.SelectSingleNode("DisplayTitle");
                    if (displayTitle != null)
                    {
                        setRefTitle(displayTitle);
                    }
                    else
                    {
                        Util.writeToWholeLog("Missing DisplayTitle in XML", true);
                        result.Add("[ERROR] Missing DisplayTitle in XML");
                    }

                    foreach (XmlNode detail in r.SelectNodes("ReleaseDetailsByTerritory"))
                    {
                        if (detail.getText("TerritoryCode") == "HK")
                        {
                            relDate = detail.getText("OriginalReleaseDate");
                            if (!string.IsNullOrEmpty(relDate))
                            {
                                string[] d = relDate.Split('-');
                                year = int.Parse(d[0]);
                                month = int.Parse(d[1]);
                            }

                            if (string.IsNullOrEmpty(displayArtist))
                            {
                                displayArtist = detail.getText("DisplayArtistName");
                            }
                        }
                        else if (detail.getText("TerritoryCode") == "Worldwide")
                        {
                            if (string.IsNullOrEmpty(displayArtist))
                            {
                                displayArtist = detail.getText("DisplayArtistName");
                            }
                        }

                        setTitle(detail.SelectNodes("Title"));
                    }

                    match = regex.Match(r.getText("Duration"));

                    if (match.Success)
                    {
                        GroupCollection result = match.Groups;

                        duration = int.Parse(result["hour"].Value).ToString("00") + ':' +
                                    int.Parse(result["minute"].Value).ToString("00") + ':' +
                                    int.Parse(result["second"].Value).ToString("00");

                    }
                    else
                    {
                        Util.writeToWholeLog("Album Duration Wrong Format", true);
                        result.Add("[Error] Album Duration Wrong Format");
                    }

                    copyright = r.getText("PLine/PLineText");

                    break;
                }
            }
        }

        public void getTracks(XmlNode rel)
        {
            XmlNodeList soundRecordings = rel.SelectNodes("SoundRecording");
            Util.writeToWholeLog("Found " + soundRecordings.Count + " SoundRecording nodes");

            int trackIndex = 0;
            foreach ( XmlNode track in soundRecordings)
            {
                trackIndex++;
                Util.writeToWholeLog("Processing track " + trackIndex);

                try
                {
                    // 使用实际的XML结构：SoundRecordingEdition/ResourceId/ISRC
                    string isrc = track.getText("SoundRecordingEdition/ResourceId/ISRC");
                    Util.writeToWholeLog("ISRC: " + isrc);

                    Track t = new Track(BASE_PATH , isrc , this);
                    Util.writeToWholeLog("Track object created");

                    Util.writeToWholeLog("Starting setRefTitle");

                // 使用 DisplayTitle 或 DisplayTitleText
                XmlNode displayTitle = track.SelectSingleNode("DisplayTitle");
                if (displayTitle != null)
                {
                    Util.writeToWholeLog("Found DisplayTitle, calling setRefTitle");
                    t.setRefTitle(displayTitle);
                    Util.writeToWholeLog("setRefTitle completed");
                }
                else
                {
                    // 尝试使用 DisplayTitleText 作为替代
                    string displayTitleText = track.getText("DisplayTitleText");
                    if (!string.IsNullOrEmpty(displayTitleText))
                    {
                        // 创建一个虚拟的 DisplayTitle 节点或直接设置标题
                        Util.writeToWholeLog("Using DisplayTitleText: " + displayTitleText);
                    }
                    else
                    {
                        Util.writeToWholeLog("Missing DisplayTitle and DisplayTitleText in track", true);
                        result.Add("[ERROR] Missing DisplayTitle and DisplayTitleText in track");
                    }
                }

                Util.writeToWholeLog("Starting setTitle");

                // 直接从 SoundRecording 节点获取信息，而不是从 SoundRecordingDetailsByTerritory
                XmlNodeList titleNodes = track.SelectNodes("DisplayTitle");
                if (titleNodes.Count > 0)
                {
                    Util.writeToWholeLog("Found " + titleNodes.Count + " DisplayTitle nodes, calling setTitle");
                    t.setTitle(titleNodes);
                    Util.writeToWholeLog("setTitle completed");
                }
                else
                {
                    // 如果没有 DisplayTitle 节点，检查是否有 DisplayTitleText
                    string displayTitleText = track.getText("DisplayTitleText");
                    if (!string.IsNullOrEmpty(displayTitleText))
                    {
                        Util.writeToWholeLog("Using DisplayTitleText for setTitle: " + displayTitleText);
                        // 这里我们需要创建一个虚拟的节点列表或跳过这个调用
                    }
                }

                Util.writeToWholeLog("Starting setArtist");

                XmlNodeList artistNodes = track.SelectNodes("DisplayArtist");
                if (artistNodes.Count > 0)
                {
                    t.setArtist(artistNodes);
                }
                Util.writeToWholeLog("setArtist call completed");

                // 从 SoundRecordingEdition 获取其他信息
                Util.writeToWholeLog("Getting SoundRecordingEdition");
                XmlNode edition = track.SelectSingleNode("SoundRecordingEdition");
                if (edition != null)
                {
                    Util.writeToWholeLog("Calling setCP");
                    t.setCP(edition.getText("PLine/PLineText"));
                    Util.writeToWholeLog("setCP completed");
                }

                // 设置 Label（如果有的话）
                Util.writeToWholeLog("Getting LabelName");
                string labelName = track.getText("LabelName");
                if (!string.IsNullOrEmpty(labelName))
                {
                    Util.writeToWholeLog("Calling setLabel: " + labelName);
                    t.setLabel(labelName);
                    Util.writeToWholeLog("setLabel completed");
                }

                // ParentalWarningType 直接在 SoundRecording 下
                Util.writeToWholeLog("Calling setExplicit");
                t.setExplicit(track.getText("ParentalWarningType") == "NotExplicit");
                Util.writeToWholeLog("setExplicit completed");

                // 技术细节在 SoundRecordingEdition/TechnicalDetails/DeliveryFile 中
                Util.writeToWholeLog("Getting TechnicalDetails/DeliveryFile");
                XmlNode deliveryFile = track.SelectSingleNode("SoundRecordingEdition/TechnicalDetails/DeliveryFile");
                Util.writeToWholeLog("DeliveryFile node: " + (deliveryFile != null ? "found" : "null"));
                if (deliveryFile != null)
                {
                    Util.writeToWholeLog("Calling setSoundFile");
                    t.setSoundFile(deliveryFile);
                    Util.writeToWholeLog("setSoundFile completed");
                }
                else
                {
                    Util.writeToWholeLog("Missing TechnicalDetails/DeliveryFile in track - setting default values");
                    // 为没有技术细节的 tracks 设置默认值，这样它们仍然可以添加到数据库
                    t.setDefaultSoundFileStatus();
                }

                    this.tracks.Add(t);
                    Util.writeToWholeLog("Track " + trackIndex + " added successfully");
                }
                catch (Exception ex)
                {
                    Util.writeToWholeLog("Error processing track " + trackIndex + ": " + ex.Message, true);
                    result.Add("[ERROR] Error processing track " + trackIndex + ": " + ex.Message);
                }
            }
            Util.writeToWholeLog("Finished processing all tracks");
        }

        private void setRefTitle(XmlNode t)
        {
            string title = t.getText("TitleText");

            if (!string.IsNullOrEmpty(title))
            {
                formalTitle = new Title(new Name(title, null));
                this.titles.Add(formalTitle);
            }
        }

        private void setTitle(XmlNodeList titles)
        {
            if (formalTitle == null)
            {
                foreach (XmlNode t in titles)
                {
                    if (t.getAttr("TitleType") == "FormalTitle")
                    {

                        Title temp = new Title(new Name(t.getText("TitleText"), t.getNullableAttr("LanguageAndScriptCode")));

                        if (formalTitle == null)
                        {
                            formalTitle = temp;
                        }
                        else
                        {
                            // formal title has been set , check priority
                            if (Util.checkPriority(formalTitle.lang, temp.lang))
                            {
                                formalTitle = temp;
                            }
                        }

                        this.titles.Add(temp);
                        // we do not break because we want to get the formal title with greatest priority
                    }
                }
            }

            foreach ( XmlNode t in titles )
            {
                if (t.getAttr("TitleType") != "FormalTitle")
                {
                    Title temp = new Title(new Name(t.getText("TitleText"), t.getNullableAttr("LanguageAndScriptCode")));
                    
                    // if running above still cannot find formal title
                    if (formalTitle == null)
                    {
                        formalTitle = temp;
                    }
                    else
                    {
                        // formal title has been set , check priority
                        // add if the lang code / title not the same
                        if ( temp.lang != formalTitle.lang || 
                            temp.name != formalTitle.name )
                        {
                            this.titles.Add(temp);
                        }
                    }
                }
            }

        }

        public void displayAlbum()
        {
            Util.writeToWholeLog("displayAlbum: Starting");

            Util.writeToWholeLog("displayAlbum: Calling addAlbumToDB");
            addAlbumToDB();
            Util.writeToWholeLog("displayAlbum: addAlbumToDB completed");

            string log = log_path + "log.txt";

            Util.writeToWholeLog("displayAlbum: formalTitle = " + (formalTitle != null ? formalTitle.name : "null"));

            if (formalTitle != null)
            {
                result.Add("Album Name: " + formalTitle.name);
            }
            else
            {
                result.Add("Album Name: Unknown Album");
                Util.writeToWholeLog("displayAlbum: formalTitle is null, using default name", true);
            }
            result.Add("MessageType: " + msgType);
            result.Add("Release Type: " + relType);
            result.Add("Release Date: " + relDate);
            result.Add("Total Duration: " + duration);
            result.Add("grid: " + grid);
            result.Add("icpn: " + icpn);
            result.Add("coverPath: " + COVER_PATH);
            result.Add("copyright: " + copyright);
            result.Add(Environment.NewLine);
            result.Add(Environment.NewLine);

            int count = 1;

            foreach ( Track t in tracks)
            {
                success = t.addTrackToDB(album_id) && success;
                result.Add("Track " + count + " : ");
                result.AddRange(t.displayTrack(album_id));
            }

            isCompleted = true;

            Util.writeFile(log, result);
        }

        private void addAlbumToDB()
        {
            Util.writeToWholeLog("addAlbumToDB: Starting");

            if (isUpdate)
            {
                success = false;
                Util.writeToWholeLog("Updating Album. Ignored.", true);
                return;
            }

            Util.writeToWholeLog("addAlbumToDB: formalTitle = " + (formalTitle != null ? formalTitle.name : "null"));

            string albumName = formalTitle != null ? formalTitle.name : "Unknown Album";

            Dictionary<string, object> para = new Dictionary<string, object>
            {
                { "`album_name`" , albumName},
                { "`grid`" , grid},
                { "`icpn`" , icpn},
                { "`releasedate`" , relDate},
                { "`duration`" , duration},
                { "`cover_link`" , DB_COVER_PATH}
            };

            Dictionary<string, object> where = new Dictionary<string, object>
            {
                { "@W1" , icpn},
                { "@W2" , duration}
            };

            lock (_lock)
            {

                string sql = "select album_id from album where icpn = @W1 and duration = @W2";
                DB.shared.select(sql, where, dr =>
               {
                   if (dr.HasRows)
                   {
                        // album exists
                        dr.Read();
                       album_id = long.Parse(dr["album_id"].ToString());

                       where = new Dictionary<string, object>
                       {
                            { "`album_id`" , album_id}
                       };

                       _r = Util.getUpdateSql("`album`", para, where);

                       DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);
                       Util.writeToWholeLog("Album Updated : " + album_id + ' ' + albumName);
                   }
                   else
                   {
                       _r = Util.getInsertSql("`album`", para);

                       dbresult = DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);
                       if (dbresult["success"] == DB.SUCCESS)
                       {
                           long id = dbresult["inert_row_id"];
                           album_id = id;

                           Util.writeToWholeLog("Album Inserted : " + album_id + ' ' + albumName);
                       }

                   }
               });

                // insert the titles
                foreach (Title t in titles)
                {
                    where = new Dictionary<string, object>
                {
                    { "@W1" , t.name },
                    { "@W2" , album_id }
                };

                    sql = "select 1 from `m3_album_name` where album_name = @W1 and album_id = @W2 and `language` is null";

                    if (!string.IsNullOrEmpty(t.lang))
                    {
                        sql = "select 1 from `m3_album_name` where album_name = @W1 and album_id = @W2 and `language` = @W3";
                        where.Add("@W3", t.lang);
                    }

                    DB.shared.select(sql, where, dr =>
                   {

                       if (dr.HasRows)
                       {
                            // album name already exists , do nth
                       }
                       else
                       {
                           para = new Dictionary<string, object>
                           {
                                { "`album_id`" , album_id },
                                { "`album_name`" , t.name },
                                { "`language`" , t.lang }
                           };

                           _r = Util.getInsertSql("`m3_album_name`", para);

                           DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);

                       }
                   });

                }

            }
        }

        public string getResult()
        {
            return '"' + formalTitle.name + "\" by " + displayArtist;
        }

        public void writeSuccess()
        {
            using (System.IO.StreamWriter file =
                    new System.IO.StreamWriter(log_success, true))
            {
                file.WriteLine(String.Empty);
            }
        }

        public void writeUpdate()
        {
            using (System.IO.StreamWriter file =
                    new System.IO.StreamWriter(log_update, true))
            {
                file.WriteLine(String.Empty);
            }
        }

        public bool isAlreadySuccess()
        {
            return File.Exists(log_success);
        }

        public bool isAlreadyUpdate()
        {
            return File.Exists(log_update);
        }

    }
}
