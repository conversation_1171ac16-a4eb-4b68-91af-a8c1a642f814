﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml;
using Sony_Song_Import.util;
using MySql.Data.MySqlClient;

namespace Sony_Song_Import.Class
{
    class Artist
    {
        private List<string> na = new string[] { "N/A", "无", "無", "UNKNOWN" }.ToList();

        // 移除全局锁，使用更细粒度的锁机制

        public long artistId = -1;
        public List<Name> names = new List<Name>();
        public List<long> roles = new List<long>();

        private List<string> result = new List<string>();

        private Album album;

        // For db
        private Dictionary<string, long> dbresult;
        private List<object> _r;

        public void setName(string name , string lang )
        {
            if (checkNameExist(name , lang) || isNameNull(name))
            {
                return;
            }
            string n = name.Trim();
            names.Add(new Name(n, lang));
        }

        public void setRole(XmlNode r)
        {
            string _r = r.InnerText.empty();
            if ( _r == "UserDefined")
            {
                _r = r.getAttr("UserDefinedValue");
            }

            // 使用role特定的锁，避免不同role之间的锁竞争
            object roleLock = Util.ConcurrencyManager.GetResourceLock("role_" + _r);
            lock (roleLock)
            {
                foreach (KeyValuePair<long, string> role in SonyImport.role)
                {
                    if (role.Value == _r)
                    {
                        if (!roles.Contains(role.Key))
                        {
                            roles.Add(role.Key);
                        }

                        return;
                    }
                }

                // insert new role to DB and gloabl variable "role"
                string query = "insert into `role` (`role`) values (@ROLE)";
                Dictionary<string, object> para = new Dictionary<string, object>
                {
                    { "@ROLE" , _r }
                };

                Dictionary<string, long> result = DB.shared.query(query, para);
                if (result["success"] == DB.SUCCESS)
                {
                    long id = result["inert_row_id"];
                    roles.Add(id);
                    SonyImport.role.Add(id, _r);
                }
            }
        }

        public bool checkNameExist(string name, string lang)
        {
            foreach (Name n in names)
            {
                if (n.name == name && n.lang == lang)
                {
                    return true;
                }
            }

            return false;
        }

        private bool isNameNull(string name)
        {
            return na.Contains(name);
        }

        public Artist(Album a)
        {
            this.album = a;
        }

        public List<string> displayArtist(long track_id)
        {
            if (track_id != -1)
            {
                addArtistToDB(track_id);
            }

            string artist = "";
            foreach (Name name in names)
            {
                artist += name.name + " -- " + 
                    name.lang + " -- " + 
                    (name.isForaml? " Formal " : " Imformal ");
            }
            result.Add(artist);

            string _role = "";
            foreach (long role in roles)
            {
                _role += SonyImport.role[role] + " ; ";
            }

            result.Add(_role);

            

            return result;
        }

        private void addArtistToDB(long track_id)
        {
            if (album.isUpdate)
            {
                return;
            }

            if (names.Count == 0)
            {
                return;
            }

            setMainArtist();

            Dictionary<string, object> where , para;

            // 使用艺术家特定的锁，基于主要艺术家名称
            string mainArtistName = names.FirstOrDefault(n => n.isForaml)?.name ?? "unknown";
            object artistLock = Util.ConcurrencyManager.GetResourceLock("artist_" + mainArtistName);
            lock (artistLock)
            {
                foreach (Name n in names)
                {
                    if (n.isForaml)
                    {
                        where = new Dictionary<string, object>
                        {
                            { "@W1" , n.name }
                        };

                        string sql = "select `artist_id` from `artist_name` " +
                                    "where `artist_name` = @W1 and `language` is null " +
                                    "order by `artist_id` asc limit 1";

                        if (n.lang != null)
                        {
                            sql = "select `artist_id` from `artist_name` " +
                                    "where `artist_name` = @W1 and `language` = @W2 " +
                                    "order by `artist_id` asc limit 1";

                            where.Add("@W2", n.lang);
                        }

                        DB.shared.select(sql, where, dr =>
                       {

                           if (dr.HasRows)
                           {
                                // artist
                                dr.Read();
                               artistId = long.Parse(dr["artist_id"].ToString());
                           }
                           else
                           {
                               para = new Dictionary<string, object> { { "`artist1`", n.name } };

                               _r = Util.getInsertSql("`artist`", para);

                               dbresult = DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);
                               if (dbresult["success"] == DB.SUCCESS)
                               {
                                   artistId = dbresult["inert_row_id"];
                               }
                               else
                               {
                                   Util.writeToWholeLog("Artist insert Error :" + n.name + " in album :" + album.BASE_PATH, true);
                                   return;
                               }
                           }
                       });
                        break;
                    }

                }

                // insert to artist name table
                foreach (Name n in names)
                {
                    where = new Dictionary<string, object>
                    {
                        { "@W1" , n.name },
                        { "@W2" , artistId }
                    };

                    string sql = "select 1 from `artist_name` where `artist_name` = @W1 and `artist_id` = @W2 and `language` is null";

                    if (!string.IsNullOrEmpty(n.lang))
                    {
                        sql = "select 1 from `artist_name` where `artist_name` = @W1 and `artist_id` = @W2 and `language` = @W3";
                        where.Add("@W3", n.lang);
                    }

                    DB.shared.select(sql, where, dr =>
                   {

                       if (dr.HasRows)
                       {
                            // artist name exists , do nth
                       }
                       else
                       {

                           para = new Dictionary<string, object>
                           {
                                { "`artist_id`" , artistId },
                                { "`artist_name`" ,  n.name },
                                { "`language`" , n.lang }
                           };

                           _r = Util.getInsertSql("`artist_name`", para);
                           dbresult = DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);
                       }
                   });
                }

                // because in every track , every artist can have different roles
                // so no checking
                foreach (long role in roles)
                {
                    where = new Dictionary<string, object>
                {
                    { "@W1" , track_id },
                    { "@W2" , artistId },
                    { "@W3" , role }
                };

                    string sql = "select 1 from `m3_artist_link` where `track_id`=@W1 and `artist_id`=@W2 and `role_id`=@W3";
                    DB.shared.select(sql, where, dr =>
                   {

                       if (dr.HasRows)
                       {
                            // role exist
                       }
                       else
                       {

                            // insert role
                            para = new Dictionary<string, object>
                           {
                                { "`track_id`" , track_id },
                                { "`artist_id`" , artistId },
                                { "`role_id`" , role }
                           };

                           _r = Util.getInsertSql("`m3_artist_link`", para);
                           dbresult = DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);

                       }
                   });
                }

            }
        }

        private void setMainArtist()
        {
            names[0].isForaml = true;
            // the last formal name
            Name _n = names[0];

            foreach (Name name in names)
            {
                if (name.lang != null)
                {
                    if (Util.langOrder.ContainsKey(name.lang.ToUpper()))
                    {
                        name.isForaml = true;
                        if (name != _n)
                        {
                            if (Util.checkPriority(_n.lang , name.lang))
                            {
                                _n.isForaml = false;
                                name.isForaml = true;
                                _n = name;
                            }
                        }
                    }
                }
            }
            
        }

    }
}
