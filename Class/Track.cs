﻿using Sony_Song_Import.util;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;
using System.Text.RegularExpressions;
using MySql.Data.MySqlClient;
using System.IO;

namespace Sony_Song_Import.Class
{
    class Track
    {
        private static Dictionary<int, string> STATUS = new Dictionary<int, string>
        {
            { 200 , "FILE SUCCESS" },
            { 404 , "FILE NOT FOUND" },
            { 405 , "FILE NOT MD5 CHECK" },
            { 500 , "FILE CORRUPTED" },
            { 501 , "FILE ENCRYPT FAIL" }
        };

        // the db object
        private static Object _lock = new Object();

        // the album this track in
        private Album album;

        private string BASE_PATH;

        public long track_id = -1;

        private string isrc = "";
        private string label = "";
        private long label_id = -1;
        private string copyright = "";
        private int _explicit = 0;

        private int disc = 1;
        private int track = 1;

        private List<Title> titles = new List<Title>();
        private Title formalTitle = null;

        private List<Artist> artists = new List<Artist>();

        private List<string> result = new List<string>();

        // to get the duration
        private Regex durReg = new Regex("[D|d]uration:.((\\d|:|\\.)*)");
        private Regex gainReg = new Regex("[T|t]rack_gain(\\D+[^-\\d])(-?\\d+\\.\\d+)");
        private Regex discReg = new Regex("_(?<disc>\\d{3,})-(?<track>\\d{3,})");
        private Match m;

        // For db
        private Dictionary<string, long> dbresult;
        private List<object> _r;

        private string[] preg_replace_pattern = new string[] { "\\.[^.]+$" };
        private string[] preg_replace_result = new string[] { "." };

        private Dictionary<string, object> soundFile = new Dictionary<string, object>
        {
            { "bitrate" , 0 },
            { "sampRate" , "" },
            { "path" , "" },
            { "isExist" , false },
            { "replaygain" , 0 },
            { "duration" , 0 },
            { "status" , CustomException.FILE_NOT_FOUND },
        };

        public Track(string bp, string isrc, Album a)
        {
            this.BASE_PATH = bp;
            this.isrc = isrc;
            this.album = a;
        }

        public void setRefTitle(XmlNode t)
        {
            string title = t.getText("TitleText");
            string sub = t.getNullableText("SubTitle");

            if (!string.IsNullOrEmpty(title))
            {
                formalTitle = new Title(new Name(title, null), sub);
                titles.Add(formalTitle);
            }
        }

        public void setTitle(XmlNodeList _t)
        {
            // we get the title from reference title
            if (formalTitle == null)
            {
                foreach (XmlNode title in _t)
                {
                    if (title.getAttr("TitleType") == "FormalTitle")
                    {
                        Title temp = new Title(new Name(
                                title.getText("TitleText"),
                                title.getNullableAttr("LanguageAndScriptCode")
                            ), title.getNullableText("SubTitle"));

                        if (formalTitle == null)
                        {
                            formalTitle = temp;
                        }
                        else
                        {
                            if (Util.checkPriority(formalTitle.lang, temp.lang))
                            {
                                formalTitle = temp;
                            }
                        }

                        this.titles.Add(temp);
                    }
                }
            }

            foreach (XmlNode title in _t)
            {
                Title temp = new Title(new Name(
                    title.getText("TitleText"),
                    title.getNullableAttr("LanguageAndScriptCode")
                ), title.getNullableText("SubTitle"));

                // if running above still cannot find formal title
                if (formalTitle == null)
                {
                    formalTitle = temp;
                }
                else
                {
                    // formal title has been set , check priority
                    // add if the lang code / title not the same
                    if (temp.lang != formalTitle.lang ||
                        temp.name != formalTitle.name)
                    {
                        this.titles.Add(temp);
                    }
                }
            }

        }

        public void setArtist(XmlNodeList da)
        {
            Util.writeToWholeLog("setArtist called with " + (da != null ? da.Count.ToString() : "null") + " nodes");

            if (da == null)
            {
                Util.writeToWholeLog("DisplayArtist NodeList is null");
                return;
            }

            foreach (XmlNode artist in da)
            {
                Util.writeToWholeLog("Processing DisplayArtist node");
                Artist a = new Artist(album);
                Util.writeToWholeLog("Artist object created");

                // 使用实际的XML结构：从 SoundRecording 节点获取 DisplayArtistName
                XmlNode soundRecordingNode = artist.ParentNode;
                Util.writeToWholeLog("soundRecordingNode: " + (soundRecordingNode != null ? soundRecordingNode.Name : "null"));

                if (soundRecordingNode != null)
                {
                    Util.writeToWholeLog("Getting DisplayArtistName from soundRecordingNode");
                    string displayArtistName = soundRecordingNode.getText("DisplayArtistName");
                    Util.writeToWholeLog("DisplayArtistName: " + displayArtistName);

                    if (!string.IsNullOrEmpty(displayArtistName))
                    {
                        Util.writeToWholeLog("Setting artist name: " + displayArtistName);
                        a.setName(displayArtistName, null);
                        Util.writeToWholeLog("Artist name set successfully");
                    }
                    else
                    {
                        // 如果没有 DisplayArtistName，尝试使用 ArtistPartyReference 作为名称
                        Util.writeToWholeLog("Getting ArtistPartyReference");
                        string partyRef = artist.getText("ArtistPartyReference");
                        Util.writeToWholeLog("ArtistPartyReference: " + partyRef);

                        if (!string.IsNullOrEmpty(partyRef))
                        {
                            a.setName(partyRef, null);
                        }
                        else
                        {
                            // 如果都没有，使用一个默认名称
                            a.setName("Unknown Artist", null);
                        }
                    }
                }

                // 处理 ArtisticRole
                Util.writeToWholeLog("Getting ArtisticRole nodes");
                XmlNodeList artisticRoles = artist.SelectNodes("ArtisticRole");
                Util.writeToWholeLog("ArtisticRole nodes: " + (artisticRoles != null ? artisticRoles.Count.ToString() : "null"));

                if (artisticRoles != null && artisticRoles.Count > 0)
                {
                    foreach (XmlNode role in artisticRoles)
                    {
                        if (role != null)
                        {
                            Util.writeToWholeLog("Setting role: " + role.InnerText);
                            a.setRole(role);
                            Util.writeToWholeLog("Role set successfully");
                        }
                    }
                }

                Util.writeToWholeLog("Adding artist to list");
                artists.Add(a);
                Util.writeToWholeLog("Artist added successfully");
            }
            Util.writeToWholeLog("setArtist completed");
        }

        public void setContri(XmlNodeList rc)
        {
            foreach (XmlNode contri in rc)
            {
                bool found = false;
                XmlNodeList name = contri.SelectNodes("PartyName");
                XmlNodeList role = contri.SelectNodes("ResourceContributorRole");

                foreach (Artist a in artists)
                {
                    foreach (XmlNode pn in name)
                    {
                        if (a.checkNameExist(pn.getText("FullName"), pn.getNullableAttr("LanguageAndScriptCode")))
                        {
                            found = true;
                            break;
                        }
                    }

                    if (found)
                    {
                        foreach (XmlNode pn in name)
                        {
                            a.setName(pn.getText("FullName"), pn.getNullableAttr("LanguageAndScriptCode"));
                        }

                        foreach (XmlNode rcr in role)
                        {
                            a.setRole(rcr);
                        }

                        break;
                    }
                }

                if (!found)
                {
                    Artist a = new Artist(album);

                    foreach (XmlNode pn in name)
                    {
                        a.setName(pn.getText("FullName"), pn.getNullableAttr("LanguageAndScriptCode"));
                    }

                    foreach (XmlNode rcr in role)
                    {
                        a.setRole(rcr);
                    }

                    this.artists.Add(a);
                }

            }
        }

        public void setLabel(string label)
        {
            this.label = label;

            lock (_lock)
            {
                foreach (KeyValuePair<long, string> lb in SonyImport.label)
                {
                    if (lb.Value == this.label)
                    {
                        label_id = lb.Key;
                        return;
                    }
                }

                // new label , add to db
                string query = "insert into `label` (`label_name` , `brand_id`) " +
                    "values (@LABEL , @BRAND)";
                Dictionary<string, object> para = new Dictionary<string, object>
                {
                    { "@LABEL" , this.label },
                    { "@BRAND" , 1 } // ** For Sony Label
                };

                Dictionary<string, long> result = DB.shared.query(query, para);
                if (result["success"] == DB.SUCCESS)
                {
                    long id = result["inert_row_id"];
                    label_id = id;
                    SonyImport.label.Add(id, this.label);
                }

            }

        }

        public void setCP(string cp)
        {
            this.copyright = cp;
        }

        public void setExplicit(bool explcit)
        {
            _explicit = explcit ? 0 : 1;
        }

        public void setSoundFile(XmlNode detail)
        {
            try
            {
                Util.writeToWholeLog("setSoundFile: Starting, detail node = " + (detail != null ? "not null" : "null"));

                if (detail == null)
                {
                    throw new ArgumentNullException("detail", "DeliveryFile node is null");
                }

                Util.writeToWholeLog("setSoundFile: Getting BitRate");
                soundFile["bitrate"] = detail.getInt("BitRate");
                Util.writeToWholeLog("setSoundFile: BitRate = " + soundFile["bitrate"]);

            Util.writeToWholeLog("setSoundFile: Getting SamplingRate node");
            XmlNode samp = detail.SelectSingleNode("SamplingRate");
            Util.writeToWholeLog("setSoundFile: SamplingRate node = " + (samp != null ? "found" : "null"));

            if (samp != null)
            {
                soundFile["sampRate"] = samp.getText() + samp.getAttr("UnitOfMeasure");
                Util.writeToWholeLog("setSoundFile: sampRate = " + soundFile["sampRate"]);
            }
            else
            {
                soundFile["sampRate"] = "Unknown";
                Util.writeToWholeLog("setSoundFile: SamplingRate node not found, using default");
            }

            Util.writeToWholeLog("setSoundFile: Getting File node");
            XmlNode file = detail.SelectSingleNode("File");
            Util.writeToWholeLog("setSoundFile: File node = " + (file != null ? "found" : "null"));

            Dictionary<string, object> _result;

            if (file == null)
            {
                if (!album.isUpdate)
                {
                    Util.writeToWholeLog("Missing MusicFile Property in MetaData", true);
                    result.Add("[ERROR] Missing MusicFile Property in MetaData");
                }

                // 创建一个默认的结果，避免空引用异常
                _result = new Dictionary<string, object>
                {
                    { "path", "" },
                    { "status", -1 },
                    { "success", false }
                };
                Util.writeToWholeLog("setSoundFile: Using default file result");
            }
            else
            {
                Util.writeToWholeLog("setSoundFile: Calling checkFileExist");
                _result = checkSonyFileExist(file, BASE_PATH);
                Util.writeToWholeLog("setSoundFile: checkFileExist completed");
            }

            soundFile["path"] = (string)_result["path"];
            soundFile["status"] = _result["status"];

            if ((bool)_result["success"])
            {
                string filePath = (string)soundFile["path"];
                string localFilePath = filePath;
                bool isTemporaryFile = false;

                // 检查是否是远程 URL
                if (filePath.StartsWith("http://") || filePath.StartsWith("https://"))
                {
                    Util.writeToWholeLog("setSoundFile: Remote file detected, attempting to download");

                    // 创建临时目录
                    string tempDir = Path.Combine(Path.GetTempPath(), "SonyImport_" + DateTime.Now.Ticks);

                    // 下载远程文件
                    Dictionary<string, object> downloadResult = Util.downloadRemoteFile(filePath, tempDir);

                    if ((bool)downloadResult["success"])
                    {
                        localFilePath = (string)downloadResult["localPath"];
                        isTemporaryFile = true;
                        Util.writeToWholeLog("setSoundFile: Remote file downloaded successfully to " + localFilePath);
                    }
                    else
                    {
                        Util.writeToWholeLog("setSoundFile: Failed to download remote file: " + downloadResult["msg"], true);
                        // 下载失败，设置默认值
                        soundFile["duration"] = "00:00:00.00";
                        soundFile["replaygain"] = 0.0;
                        disc = 1;
                        track = 1;
                        return; // 退出处理
                    }
                }

                // 处理本地文件（原始本地文件或下载的临时文件）
                if (File.Exists(localFilePath))
                {
                    Util.writeToWholeLog("setSoundFile: Processing local file with ffmpeg: " + localFilePath);

                    try
                    {
                        // get duration
                        string result = Util.execute("-i \"" + localFilePath + "\"");
                        m = durReg.Match(result);

                        if (m.Success)
                        {
                            //Means the output has cantained the string "Duration"
                            soundFile["duration"] = m.Groups[1].Value;
                        }

                        // get replay gain
                        result = Util.execute("-i \"" + localFilePath + "\" -af \"replaygain\" -f null /dev/null ");
                        m = gainReg.Match(result);

                        if (m.Success)
                        {
                            //Means the output has cantained the string "track_gain"
                            soundFile["replaygain"] = double.Parse(m.Groups[2].Value ?? "0") * 100;
                        }

                        // get disc no and track no
                        m = discReg.Match(localFilePath);

                        if (m.Success)
                        {
                            disc = int.Parse(m.Groups["disc"].Value ?? "1");
                            track = int.Parse(m.Groups["track"].Value ?? "1");
                        }
                        else
                        {
                            Util.writeToWholeLog("Disc No and Track No from Path not match regular expression", true);
                            this.result.Add("[Error] Disc No and Track No from Path not match regular expression.");
                        }

                        // 更新 soundFile 中的路径为本地文件路径（用于后续加密和上传）
                        soundFile["path"] = localFilePath;
                        soundFile["isTemporary"] = isTemporaryFile;
                    }
                    catch (Exception ex)
                    {
                        Util.writeToWholeLog("setSoundFile: Error processing file with ffmpeg: " + ex.Message, true);
                        // 设置默认值
                        soundFile["duration"] = "00:00:00.00";
                        soundFile["replaygain"] = 0.0;
                        disc = 1;
                        track = 1;
                    }
                }
                else
                {
                    Util.writeToWholeLog("setSoundFile: Local file does not exist: " + localFilePath, true);
                    soundFile["duration"] = "00:00:00.00";
                    soundFile["replaygain"] = 0.0;
                    disc = 1;
                    track = 1;
                }
            }
            else
            {
                string msg = (string)_result["msg"];

                Util.writeToWholeLog("Cover fail :" + soundFile["path"] +
                    " with reason : " + msg, true);

                result.Add("[ERROR] " + soundFile["path"] + " fail because " + msg);
            }
            }
            catch (Exception ex)
            {
                Util.writeToWholeLog("setSoundFile: Exception occurred - " + ex.Message, true);
                Util.writeToWholeLog("setSoundFile: Stack trace - " + ex.StackTrace, true);

                // 设置默认值以避免后续的空引用异常
                soundFile["status"] = CustomException.FILE_NOT_FOUND;
                soundFile["path"] = "Error in processing";
                soundFile["bitrate"] = 0;
                soundFile["sampRate"] = "Unknown";
                soundFile["duration"] = "00:00:00.00";
                soundFile["replaygain"] = 0.0;
                disc = 1;
                track = 1;

                throw; // 重新抛出异常以便上层处理
            }

        }

        public bool addTrackToDB(long album_id)
        {
            Util.writeToWholeLog("addTrackToDB: Starting for track: " + (formalTitle != null ? formalTitle.name : "null"));

            if (album.isUpdate)
            {
                Util.writeToWholeLog("addTrackToDB: Album is update, returning false");
                return false;
            }

            int _status = (int)soundFile["status"];
            Util.writeToWholeLog("addTrackToDB: Sound file status = " + _status);

            if ( _status != CustomException.FILE_SUCCESS)
            {
                Util.writeToWholeLog("Sound Track File Error : " + _status + ' ' + Track.STATUS[_status]);
                Util.writeToWholeLog("Track File <" + formalTitle.name + "> (" +
                    (string)soundFile["path"] + ") did not add to DB", true);
                return false;
            }

            string title = formalTitle.name + (formalTitle.sub == null ? "" : " (" + formalTitle.sub + ')');
            Util.writeToWholeLog("addTrackToDB: Track title = " + title);
            Util.writeToWholeLog("addTrackToDB: ISRC = " + isrc);
            Util.writeToWholeLog("addTrackToDB: Album ID = " + album_id);

            Dictionary<string, object> para = new Dictionary<string, object>
            {
                {"`duration`" , soundFile["duration"] },
                {"`isrc`" , isrc },
                {"`discno`" , disc },
                {"`trackno`" , track },
                {"`trackname`" , title },
                {"`releasetype`" , album.relType },
                {"`year`" , album.year },
                {"`month`" , album.month },
                {"`bitrate`" , soundFile["bitrate"] },
                {"`samplingrate`" , soundFile["sampRate"] },
                {"`copyright`" , copyright },
                {"`replaygain`" , soundFile["replaygain"] },
                {"`explicit`" , _explicit },
                {"`dlanguage`" , formalTitle.lang }
            };

            Dictionary<string, object> where = new Dictionary<string, object>
            {
                {"@A1" , isrc },
                {"@A2" , album.album_id }
            };

            Util.writeToWholeLog("addTrackToDB: Entering lock section");
            lock (_lock)
            {
                Util.writeToWholeLog("addTrackToDB: Inside lock, preparing SQL query");

                string sql = "select t.track_id from track t , m3_album_link malk where t.isrc = @A1 " +
                "and malk.album_id = @A2 and malk.track_id = t.track_id";

                Util.writeToWholeLog("addTrackToDB: SQL query = " + sql);
                Util.writeToWholeLog("addTrackToDB: About to execute DB.shared.select");

                bool fail = false;

                DB.shared.select(sql, where, dr =>
               {
                   Util.writeToWholeLog("addTrackToDB: Inside DB callback, checking HasRows");
                   if (dr.HasRows)
                   {
                        Util.writeToWholeLog("addTrackToDB: Track exists, updating");
                        // track exists
                        dr.Read();
                       track_id = long.Parse(dr["track_id"].ToString());
                       Util.writeToWholeLog("addTrackToDB: Found existing track_id = " + track_id);

                       where = new Dictionary<string, object>
                       {
                            { "`track_id`" , track_id}
                       };

                       _r = Util.getUpdateSql("`track`", para, where);
                       Util.writeToWholeLog("addTrackToDB: About to execute update query");

                       DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);
                       Util.writeToWholeLog("addTrackToDB: Update query completed");

                       DB.shared.query("delete from `m3_artist_link` where `track_id` = " + track_id);
                       Util.writeToWholeLog("addTrackToDB: Artist link deletion completed");

                       Util.writeToWholeLog("Track Updated : " + track_id + ' ' + title);

                   }
                   else
                   {
                        Util.writeToWholeLog("addTrackToDB: Track does not exist, inserting new track");
                        // insert track

                       string bin_path = ((string)soundFile["path"]).Replace(SonyImport.FOLDER_LOCATION, SonyImport.LOG_LOCATION)
                           .Replace("resources/", string.Empty).PregReplace(preg_replace_pattern, preg_replace_result) + "bin";

                       string db_path = bin_path.Replace(SonyImport.LOG_LOCATION, SonyImport.NAS_DIR);
                       Util.writeToWholeLog("addTrackToDB: Bin path = " + bin_path);
                       Util.writeToWholeLog("addTrackToDB: DB path = " + db_path);

                       Util.writeToWholeLog("addTrackToDB: About to encrypt track");
                       encrypt_track((string)soundFile["path"], bin_path);

                       if (File.Exists(bin_path))
                       {
                            // encrypt success
                            para.Add("`md5`", Util.getMD5(bin_path));

                           if (Util.uploadFile(bin_path, db_path))
                           {
                               para.Add("`filelocation`", db_path);
                               Util.writeToWholeLog("Upload Track Success");
                               File.Delete(bin_path);

                               // 清理临时下载的文件
                               if (soundFile.ContainsKey("isTemporary") && (bool)soundFile["isTemporary"])
                               {
                                   try
                                   {
                                       string tempFile = (string)soundFile["path"];
                                       if (File.Exists(tempFile))
                                       {
                                           File.Delete(tempFile);
                                           Util.writeToWholeLog("Temporary downloaded file deleted: " + tempFile);

                                           // 删除临时目录（如果为空）
                                           string tempDir = Path.GetDirectoryName(tempFile);
                                           if (Directory.Exists(tempDir) && Directory.GetFiles(tempDir).Length == 0)
                                           {
                                               Directory.Delete(tempDir);
                                               Util.writeToWholeLog("Temporary directory deleted: " + tempDir);
                                           }
                                       }
                                   }
                                   catch (Exception ex)
                                   {
                                       Util.writeToWholeLog("Failed to delete temporary file: " + ex.Message, true);
                                   }
                               }
                           }
                           else
                           {
                               Util.writeToWholeLog("Upload Track Fail : " + (string)soundFile["path"], true);
                               result.Add("[ERROR] Upload Track Fail : " + (string)soundFile["path"]);
                               File.Delete(bin_path);

                               // 即使上传失败也要清理临时文件
                               if (soundFile.ContainsKey("isTemporary") && (bool)soundFile["isTemporary"])
                               {
                                   try
                                   {
                                       string tempFile = (string)soundFile["path"];
                                       if (File.Exists(tempFile))
                                       {
                                           File.Delete(tempFile);
                                           Util.writeToWholeLog("Temporary downloaded file deleted after upload failure: " + tempFile);

                                           // 删除临时目录（如果为空）
                                           string tempDir = Path.GetDirectoryName(tempFile);
                                           if (Directory.Exists(tempDir) && Directory.GetFiles(tempDir).Length == 0)
                                           {
                                               Directory.Delete(tempDir);
                                               Util.writeToWholeLog("Temporary directory deleted: " + tempDir);
                                           }
                                       }
                                   }
                                   catch (Exception ex)
                                   {
                                       Util.writeToWholeLog("Failed to delete temporary file after upload failure: " + ex.Message, true);
                                   }
                               }

                               fail = true;
                           }

                       }
                       else
                       {
                           Util.writeToWholeLog("Sound Encryption fail :" + (string)soundFile["path"], true);
                           result.Add("[ERROR] Sound Encryption fail :" + (string)soundFile["path"]);
                           soundFile["status"] = CustomException.FILE_ENCRYPT_FAIL;
                           fail = true;
                       }


                       _r = Util.getInsertSql("`track`", para);

                       dbresult = DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);
                       if (dbresult["success"] == DB.SUCCESS)
                       {
                           track_id = dbresult["inert_row_id"];

                            // insert the album link
                            para = new Dictionary<string, object>
                           {
                                { "`track_id`" , track_id },
                                { "`album_id`" , album_id }
                           };

                           _r = Util.getInsertSql("`m3_album_link`", para);
                           dbresult = DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);

                            // insert the label link only if label_id is valid
                           if (label_id > 0)
                           {
                               para = new Dictionary<string, object>
                               {
                                    { "`track_id`" , track_id },
                                    { "`label_id`" , label_id }
                               };

                               _r = Util.getInsertSql("`label_link`", para);
                               dbresult = DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);
                               Util.writeToWholeLog("Label link inserted for label_id: " + label_id);
                           }
                           else
                           {
                               Util.writeToWholeLog("Skipping label link insertion - no valid label_id");
                           }

                           Util.writeToWholeLog("Track Inserted : " + track_id + ' ' + title);
                       }
                       else
                       {
                           Util.writeToWholeLog("Track Inserted Failure : " + title + " in album : " + album.BASE_PATH);
                       }
                   }
               });

                Util.writeToWholeLog("addTrackToDB: DB.shared.select callback completed");

                if (fail)
                {
                    Util.writeToWholeLog("addTrackToDB: Fail flag is true, returning false");
                    return false;
                }

                Util.writeToWholeLog("addTrackToDB: Processing track titles");

                foreach (Title t in titles)
                {
                    where = new Dictionary<string, object>
                    {
                        { "@W1" , t.name },
                        { "@W2" , track_id }
                    };

                    sql = "select 1 from `track_name` where `track_name` = @W1 and `track_id` = @W2";

                    bool langNull = string.IsNullOrEmpty(t.lang);
                    bool subNull = string.IsNullOrEmpty(t.sub);

                    if (!langNull || !subNull)
                    {
                        if (!langNull && !subNull)
                        {
                            sql += " and `language` = @W3 and `subtitle` = @W4";
                            where.Add("@W3", t.lang);
                            where.Add("@W4", t.sub);
                        }
                        else if (!langNull)
                        {
                            sql += " and `language` = @W3 and `subtitle` is null";
                            where.Add("@W3", t.lang);
                        }
                        else
                        {
                            sql += " and `language` is null and `subtitle` = @W3";
                            where.Add("@W3", t.sub);
                        }

                    }
                    else
                    {
                        sql += " and `language` is null and `subtitle` is null";
                    }

                    DB.shared.select(sql, where, dr =>
                   {
                       if (dr.HasRows)
                       {
                            // album name already exists , do nth
                       }
                       else
                       {
                           para = new Dictionary<string, object>
                           {
                                { "`track_id`" , track_id },
                                { "`track_name`" , t.name },
                                { "`language`" , t.lang },
                                { "`subtitle`" , t.sub }
                           };

                           _r = Util.getInsertSql("`track_name`", para);
                           DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);

                           if (t.name.Contains(' '))
                           {
                               para = new Dictionary<string, object>
                               {
                                    { "`track_id`" , track_id },
                                    { "`track_name`" , t.name.Replace(" ", String.Empty) },
                                    { "`language`" , t.lang },
                                    { "`subtitle`" , t.sub }
                               };

                               _r = Util.getInsertSql("`track_name`", para);
                               DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);
                           }

                       }
                   });

                }
            }
            Util.writeToWholeLog("addTrackToDB: Exiting lock section, returning true");
            return true;
        }

        public List<string> displayTrack(long album_id)
        {
            foreach (Title t in titles)
            {
                result.Add(t.name + " (" + t.sub + ") in " + t.lang);
            }

            result.Add("disc : " + disc);
            result.Add("track : " + track);
            result.Add("isrc : " + isrc);
            result.Add("year : " + album.year);
            result.Add("month : " + album.month);
            result.Add("status : " + soundFile["status"].ToString());
            result.Add("duration : " + soundFile["duration"].ToString());
            result.Add("replaygain : " + soundFile["replaygain"].ToString());
            result.Add("bitrate : " + soundFile["bitrate"].ToString());
            result.Add("sampRate : " + soundFile["sampRate"].ToString());
            result.Add("path : " + soundFile["path"].ToString());
            result.Add("label : " + label);
            result.Add("copyright : " + copyright);
            result.Add("explicit : " + _explicit);
            result.Add(Environment.NewLine);

            int count = 1;

            foreach (Artist a in artists)
            {
                result.Add((count++).ToString() + '.');
                result.AddRange(a.displayArtist(track_id));
            }

            result.Add(Environment.NewLine);
            result.Add(Environment.NewLine);
            return result;
        }

        private bool encrypt_track(string source , string bin)
        {
            if (File.Exists(source))
            {
                // 确保目标目录存在
                string binDirectory = Path.GetDirectoryName(bin);
                if (!Directory.Exists(binDirectory))
                {
                    Directory.CreateDirectory(binDirectory);
                    Util.writeToWholeLog("Created directory for encrypted file: " + binDirectory);
                }

                FileStream fs = File.OpenRead(source);

                using (FileStream target = new FileStream(bin, FileMode.Create))
                {
                    // can up to 630 MB? https://stackoverflow.com/questions/2030847/best-way-to-read-a-large-file-into-a-byte-array-in-c#comment21728761_2030865
                    byte[] original = File.ReadAllBytes(source);
                    fs.Close();

                    int size = original.Length;
                    byte[] encrypted = new byte[size];

                    for (int i=0; i< size; i++)
                    {
                        encrypted[i] = (byte)~original[i];
                    }

                    target.Write(encrypted, 0, size);
                    target.Flush();
                    target.Close();
                    target.Dispose();

                    return true;
                }
            }

            return false;
        }

        // 专门处理 Sony XML 格式的文件检查方法
        private Dictionary<string, object> checkSonyFileExist(XmlNode file, string basePath)
        {
            Util.writeToWholeLog("checkSonyFileExist: Starting file check");

            Dictionary<string, object> result = new Dictionary<string, object>{
                { "success" , false },
                { "path" , "" },
                { "status" , -1 },
                { "msg" , "" }
            };

            try
            {
                // Sony XML 格式使用 URI 而不是 FilePath + FileName
                XmlNode uriNode = file.SelectSingleNode("URI");
                if (uriNode == null)
                {
                    throw new CustomException("Missing URI in File node", CustomException.FILE_NOT_FOUND);
                }

                string uri = uriNode.InnerText;
                Util.writeToWholeLog("checkSonyFileExist: URI = " + uri);

                // 优先检查本地文件：从URI中提取文件名
                string fileName = "";
                try
                {
                    Uri uriObj = new Uri(uri);
                    string path = uriObj.LocalPath;
                    fileName = System.IO.Path.GetFileName(path);
                    Util.writeToWholeLog("checkSonyFileExist: Extracted filename = " + fileName);
                }
                catch (Exception ex)
                {
                    Util.writeToWholeLog("checkSonyFileExist: Failed to extract filename from URI: " + ex.Message, true);
                }

                // 如果成功提取文件名，检查本地resources目录
                if (!string.IsNullOrEmpty(fileName))
                {
                    string resourcesDir = System.IO.Path.Combine(basePath, "resources");
                    string localFilePath = System.IO.Path.Combine(resourcesDir, fileName);
                    Util.writeToWholeLog("checkSonyFileExist: Checking local file: " + localFilePath);

                    // 如果直接文件名不存在，尝试查找带前缀的文件
                    if (!File.Exists(localFilePath))
                    {
                        Util.writeToWholeLog("checkSonyFileExist: Direct filename not found, searching for prefixed files");

                        if (Directory.Exists(resourcesDir))
                        {
                            // 查找所有以fileName结尾的文件
                            string[] matchingFiles = Directory.GetFiles(resourcesDir, "*" + fileName);
                            if (matchingFiles.Length > 0)
                            {
                                localFilePath = matchingFiles[0]; // 使用第一个匹配的文件
                                Util.writeToWholeLog("checkSonyFileExist: Found prefixed file: " + localFilePath);
                            }
                            else
                            {
                                Util.writeToWholeLog("checkSonyFileExist: No matching files found in resources directory");
                            }
                        }
                    }

                    if (File.Exists(localFilePath))
                    {
                        Util.writeToWholeLog("checkSonyFileExist: Local file found, checking MD5");

                        // 检查MD5校验
                        XmlNode hashNode = file.SelectSingleNode("HashSum/HashSumValue");
                        if (hashNode != null)
                        {
                            string expectedMD5 = hashNode.InnerText;
                            string actualMD5 = Util.getMD5(localFilePath);

                            Util.writeToWholeLog("checkSonyFileExist: Expected MD5 = " + expectedMD5);
                            Util.writeToWholeLog("checkSonyFileExist: Actual MD5 = " + actualMD5);

                            if (expectedMD5.Equals(actualMD5, StringComparison.OrdinalIgnoreCase))
                            {
                                // 本地文件存在且MD5校验通过，优先使用本地文件
                                result["success"] = true;
                                result["path"] = localFilePath;
                                result["status"] = CustomException.FILE_SUCCESS;
                                result["msg"] = "Local file found and verified";
                                Util.writeToWholeLog("checkSonyFileExist: Using local file (MD5 verified): " + localFilePath);
                                return result;
                            }
                            else
                            {
                                Util.writeToWholeLog("checkSonyFileExist: Local file MD5 mismatch, will use remote URI", true);
                            }
                        }
                        else
                        {
                            // 没有MD5信息，直接使用本地文件
                            result["success"] = true;
                            result["path"] = localFilePath;
                            result["status"] = CustomException.FILE_SUCCESS;
                            result["msg"] = "Local file found (no MD5 check)";
                            Util.writeToWholeLog("checkSonyFileExist: Using local file (no MD5 check): " + localFilePath);
                            return result;
                        }
                    }
                    else
                    {
                        Util.writeToWholeLog("checkSonyFileExist: Local file not found: " + localFilePath);
                    }
                }

                // 如果本地文件不存在或校验失败，使用远程URI作为备选
                result["success"] = true;
                result["path"] = uri;
                result["status"] = CustomException.FILE_SUCCESS;
                result["msg"] = "Remote file URI found";
                Util.writeToWholeLog("checkSonyFileExist: Using remote URI as fallback: " + uri);
            }
            catch (Exception ex)
            {
                result["success"] = false;
                result["status"] = CustomException.FILE_NOT_FOUND;
                result["msg"] = ex.Message;
                Util.writeToWholeLog("checkSonyFileExist: Error - " + ex.Message, true);
            }

            return result;
        }

        // 为没有技术细节的 tracks 设置默认的文件状态
        public void setDefaultSoundFileStatus()
        {
            Util.writeToWholeLog("setDefaultSoundFileStatus: Setting default values for track without technical details");

            // 设置默认值，使 track 可以添加到数据库
            soundFile["status"] = CustomException.FILE_NOT_FOUND;
            soundFile["path"] = "No technical details available";
            soundFile["duration"] = "00:00:00.00";
            soundFile["bitrate"] = 0;
            soundFile["sampRate"] = "Unknown";
            soundFile["replaygain"] = 0.0;

            // 设置默认的 disc 和 track 编号
            disc = 1;
            track = 1;

            Util.writeToWholeLog("setDefaultSoundFileStatus: Default values set successfully");
        }

    }
}
