﻿using System;
using System.Linq;
using System.IO;
using System.Xml;
using System.Collections.Generic;
using System.Security.Cryptography;
using Newtonsoft.Json;
using Renci.SshNet;
using Renci.SshNet.Sftp;
using Renci.SshNet.Common;
using System.Diagnostics;

namespace Sony_Song_Import.util
{
    class Util
    {
        public static Dictionary<string, int> langOrder = new Dictionary<string, int>
        {
            { "ZH-HANT" , 0 } ,
            { "ZH-HANS" , 1 },
            { "EN" , 2 }
        };

        private static Object _lock = new Object();

        public static void writeToWholeLog(string line, bool isError = false)
        {
            if (string.IsNullOrEmpty(SonyImport.log_text_file))
            {
                return;
            }

            lock (_lock)
            {
                using (System.IO.StreamWriter file =
                    new System.IO.StreamWriter(SonyImport.log_text_file, true))
                {
                    string final = getCurrent() + " : " + (isError ? "[ERROR] " : "") + line;
                    file.WriteLine(final);
                    Console.WriteLine(final);
                }
            }

        }

        public static void writeFile(string path, List<string> result, bool append = false)
        {
            using (System.IO.StreamWriter file =
                    new System.IO.StreamWriter(path, append))
            {
                foreach (string line in result)
                {
                    file.WriteLine(line);
                }
            }
        }

        public static void writeRecord(Dictionary<string, List<string>> record)
        {
            if (string.IsNullOrEmpty(SonyImport.log_record_file))
            {
                return;
            }

            using (System.IO.StreamWriter file =
                    new System.IO.StreamWriter(SonyImport.log_record_file))
            {
                string final = JsonConvert.SerializeObject(record);
                file.WriteLine(final);
            }
        }

        public static string getCurrent()
        {
            return DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }

        public static Dictionary<string, object> checkFileExist(XmlNode file, string path)
        {
            string fp = path +
                file.SelectSingleNode("FilePath").InnerText +
                file.SelectSingleNode("FileName").InnerText;

            Dictionary<string, object> result = new Dictionary<string, object>{
                { "success" , false },
                { "path" , fp },
                { "status" , -1 },
                { "msg" , "" }
            };

            try
            {
                if (File.Exists(fp))
                {
                    if (file.SelectSingleNode("HashSum/HashSumAlgorithmType").InnerText == "MD5")
                    {
                        string md5 = file.SelectSingleNode("HashSum/HashSum").InnerText;

                        if (getMD5(fp) == md5)
                        {
                            result["success"] = true;
                            result["status"] = CustomException.FILE_SUCCESS;
                        }
                        else
                        {
                            throw new CustomException("File corrupted", CustomException.FILE_CORRUPTED);
                        }
                    }
                    else
                    {
                        throw new CustomException("Not MD5 check", CustomException.FILE_NOT_MD5_CHECK);
                    }
                }
                else
                {
                    throw new CustomException("File not exists", CustomException.FILE_NOT_FOUND);
                }

            }
            catch (CustomException ex)
            {
                result["success"] = false;
                result["status"] = ex.status;
                result["msg"] = ex.msg;
            }

            return result;

        }

        public static string getMD5(string path)
        {
            using (var md5 = MD5.Create())
            {
                using (var stream = File.OpenRead(path))
                {
                    return BitConverter.ToString(md5.ComputeHash(stream)).Replace("-", String.Empty).ToLower();
                }
            }
        }

        public static bool uploadFileBySFTP(string fpath, string tpath)
        {
            // 创建连接信息，设置连接超时
            var connectionInfo = new PasswordConnectionInfo(AppCommon.FTPServer, 22, AppCommon.FTPUser, AppCommon.FTPPassword);
            connectionInfo.Timeout = TimeSpan.FromSeconds(30);

            SftpClient ftp = new SftpClient(connectionInfo);

            var fileStream = new FileStream(fpath, FileMode.Open);

            try
            {
                writeToWholeLog("SFTP: Attempting to connect to " + AppCommon.FTPServer + ":22 as " + AppCommon.FTPUser);
                ftp.Connect();

                if (ftp.IsConnected)
                {
                    writeToWholeLog("SFTP: Connected successfully");
                    CreateDirectoryRecursively(ftp, "/web3/" + tpath);

                    if (fileStream != null)
                    {
                        writeToWholeLog("SFTP: Uploading file to /web3/" + tpath);
                        ftp.UploadFile(fileStream, "/web3/" + tpath, null);
                        writeToWholeLog("SFTP: File uploaded successfully");
                    }
                    else
                    {
                        throw new Exception("File Not Found.");
                    }
                }
                else
                {
                    writeToWholeLog("SFTP: Failed to establish connection", true);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                writeToWholeLog("SFTP ERROR : " + ex.Message, true);
                writeToWholeLog("SFTP ERROR Details: " + ex.ToString(), true);
            }
            finally
            {
                if (fileStream != null)
                {
                    fileStream.Close();
                }
                ftp.Disconnect();
                ftp.Dispose();
            }
            return false;
        }

        private static void CreateDirectoryRecursively(SftpClient client, string path)
        {
            string current = "";

            if (path[0] == '/')
            {
                path = path.Substring(1);
            }

            while (!string.IsNullOrEmpty(path))
            {
                int p = path.IndexOf('/');
                current += '/';
                if (p >= 0)
                {
                    current += path.Substring(0, p);
                    path = path.Substring(p + 1);
                }
                else if (p == path.LastIndexOf('/'))
                {
                    return;
                }
                else
                {
                    current += path;
                    path = "";
                }

                try
                {
                    SftpFileAttributes attrs = client.GetAttributes(current);
                    if (!attrs.IsDirectory)
                    {
                        throw new Exception("not directory");
                    }
                }
                catch (SftpPathNotFoundException)
                {
                    client.CreateDirectory(current);
                }
            }
        }

        public static bool checkPriority(string olds, string news)
        {
            // if new lang code is null , must be false
            if (string.IsNullOrEmpty(news))
            {
                return false;
            }

            string ub = news.ToUpper();

            // if old lang code is null , check if the new is in array
            if (string.IsNullOrEmpty(olds))
            {
                return langOrder.ContainsKey(ub);
            }

            string ua = olds.ToUpper();

            if (!langOrder.ContainsKey(ua))
            {
                return langOrder.ContainsKey(ub);
            }

            if (!langOrder.ContainsKey(ub))
            {
                return false;
            }

            return langOrder[ua] > langOrder[ub];

        }

        public static string execute(string argu)
        {
            Process cmd = new Process();

            // 根据操作系统选择正确的ffmpeg可执行文件名
            string ffmpegExecutable = Environment.OSVersion.Platform == PlatformID.Win32NT ? "ffmpeg.exe" : "ffmpeg";
            cmd.StartInfo.FileName = ffmpegExecutable;
            cmd.StartInfo.Arguments = argu;
            cmd.StartInfo.RedirectStandardOutput = true;
            cmd.StartInfo.RedirectStandardError = true;
            cmd.StartInfo.CreateNoWindow = true;
            cmd.StartInfo.UseShellExecute = false;

            string output = "";

            if (!cmd.Start())
            {
                Console.WriteLine("Error starting");
                return output;
            }

            StreamReader reader = cmd.StandardError;
            output = reader.ReadToEnd();

            cmd.WaitForExit();
            cmd.Close();
            cmd.Dispose();
            reader.Close();
            reader.Dispose();

            return output;
        }

        public static string dirname(string path)
        {
            FileInfo info = new FileInfo(path);
            return info.DirectoryName;
        }

        public static List<object> getInsertSql(string table, Dictionary<string, object> p)
        {
            List<object> result = new List<object>();
            string sql = "insert into " + table + ' ';
            Dictionary<string, object> para = new Dictionary<string, object>();
            string[] keys = p.Keys.ToArray();
            object[] values = p.Values.ToArray();
            List<string> bindKeys = new List<string>();

            string _b = "@A";
            int count = 1;
            foreach (object o in values)
            {
                string _k = _b + (count++);
                para.Add(_k, o);
                bindKeys.Add(_k);
            }

            sql += "(" + string.Join(",", keys) + ") values (" + string.Join(",", bindKeys.ToArray()) + ")";

            result.Add(sql);
            result.Add(para);
            return result;
        }

        public static List<object> getUpdateSql(string table, Dictionary<string, object> f, Dictionary<string, object> w )
        {
            List<object> result = new List<object>();
            string sql = "update " + table + " set ";
            Dictionary<string, object> para = new Dictionary<string, object>();

            List<string> bindUpdate = new List<string>();
            List<string> bindWhere = new List<string>();

            string _b = "@A";
            int count = 1;
            foreach (KeyValuePair<string , object> o in f)
            {
                string _k = _b + (count++);
                para.Add(_k, o.Value);
                bindUpdate.Add(o.Key + '=' + _k);
            }

            foreach (KeyValuePair<string, object> o in w)
            {
                string _k = _b + (count++);
                para.Add(_k, o.Value);
                bindWhere.Add(o.Key + '=' + _k);
            }

            sql += string.Join("," , bindUpdate) + " where " + string.Join(" and " , bindWhere);

            result.Add(sql);
            result.Add(para);
            return result;
        }

        public static bool uploadFile(string localPath, string remotePath)
        {
            try
            {
                writeToWholeLog("Starting file upload: " + localPath + " -> " + remotePath);

                // 使用SFTP上传文件
                bool result = uploadFileBySFTP(localPath, remotePath);

                if (result)
                {
                    writeToWholeLog("File upload successful: " + remotePath);
                }
                else
                {
                    writeToWholeLog("File upload failed: " + remotePath, true);
                }

                return result;
            }
            catch (Exception ex)
            {
                writeToWholeLog("Upload file error: " + ex.Message, true);
                return false;
            }
        }

        public static Dictionary<string, object> downloadRemoteFile(string remoteUrl, string tempDir)
        {
            Dictionary<string, object> result = new Dictionary<string, object>
            {
                { "success", false },
                { "localPath", "" },
                { "error", "" }
            };

            try
            {
                writeToWholeLog("Starting remote file download: " + remoteUrl);

                // 创建临时目录
                if (!Directory.Exists(tempDir))
                {
                    Directory.CreateDirectory(tempDir);
                }

                // 从URL获取文件名
                string fileName = Path.GetFileName(new Uri(remoteUrl).LocalPath);
                if (string.IsNullOrEmpty(fileName))
                {
                    fileName = "downloaded_file_" + DateTime.Now.Ticks;
                }

                string localPath = Path.Combine(tempDir, fileName);

                // 使用WebClient下载文件
                using (var client = new System.Net.WebClient())
                {
                    client.DownloadFile(remoteUrl, localPath);
                }

                if (File.Exists(localPath))
                {
                    result["success"] = true;
                    result["localPath"] = localPath;
                    writeToWholeLog("Remote file download successful: " + localPath);
                }
                else
                {
                    result["error"] = "Downloaded file not found";
                    writeToWholeLog("Downloaded file not found: " + localPath, true);
                }
            }
            catch (Exception ex)
            {
                result["error"] = ex.Message;
                writeToWholeLog("Download remote file error: " + ex.Message, true);
            }

            return result;
        }
    }
}
