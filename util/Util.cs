﻿using System;
using System.Linq;
using System.IO;
using System.Xml;
using System.Collections.Generic;
using System.Security.Cryptography;
using Newtonsoft.Json;
using Renci.SshNet;
using Renci.SshNet.Sftp;
using Renci.SshNet.Common;
using System.Diagnostics;

namespace Sony_Song_Import.util
{
    class Util
    {
        public static Dictionary<string, int> langOrder = new Dictionary<string, int>
        {
            { "ZH-HANT" , 0 } ,
            { "ZH-HANS" , 1 },
            { "EN" , 2 }
        };

        private static Object _lock = new Object();

        // 并发控制管理器
        public static class ConcurrencyManager
        {
            // 不同资源类型的锁
            private static readonly object _logLock = new object();
            private static readonly object _dbConnectionLock = new object();
            private static readonly Dictionary<string, object> _resourceLocks = new Dictionary<string, object>();
            private static readonly object _resourceLocksLock = new object();

            // 获取特定资源的锁对象
            public static object GetResourceLock(string resourceKey)
            {
                lock (_resourceLocksLock)
                {
                    if (!_resourceLocks.ContainsKey(resourceKey))
                    {
                        _resourceLocks[resourceKey] = new object();
                    }
                    return _resourceLocks[resourceKey];
                }
            }

            // 日志锁
            public static object LogLock => _logLock;

            // 数据库连接锁（如果需要）
            public static object DbConnectionLock => _dbConnectionLock;

            // 清理不再使用的资源锁
            public static void CleanupResourceLocks()
            {
                lock (_resourceLocksLock)
                {
                    _resourceLocks.Clear();
                }
            }
        }

        // Log levels
        public enum LogLevel
        {
            DEBUG = 0,    // Detailed debugging info (DB operations, detailed steps)
            INFO = 1,     // General information (processing steps)
            SUCCESS = 2,  // Success operations (uploads, inserts)
            WARNING = 3,  // Warnings
            ERROR = 4     // Errors only
        }

        // Current log level - can be configured
        public static LogLevel CurrentLogLevel = LogLevel.SUCCESS;

        public static void writeToWholeLog(string line, bool isError = false, LogLevel level = LogLevel.INFO)
        {
            if (string.IsNullOrEmpty(SonyImport.log_text_file))
            {
                return;
            }

            // Skip logging if current level is higher than message level (except for errors)
            if (level < CurrentLogLevel && !isError)
                return;

            lock (ConcurrencyManager.LogLock)
            {
                using (System.IO.StreamWriter file =
                    new System.IO.StreamWriter(SonyImport.log_text_file, true))
                {
                    string prefix = "";
                    if (isError)
                        prefix = "[ERROR] ";
                    else if (level == LogLevel.SUCCESS)
                        prefix = "[SUCCESS] ";
                    else if (level == LogLevel.WARNING)
                        prefix = "[WARNING] ";
                    else if (level == LogLevel.DEBUG)
                        prefix = "[DEBUG] ";

                    string final = getCurrent() + " : " + prefix + line;
                    file.WriteLine(final);
                    Console.WriteLine(final);
                }
            }
        }

        // Convenience methods for different log levels
        public static void writeDebugLog(string line)
        {
            writeToWholeLog(line, false, LogLevel.DEBUG);
        }

        public static void writeSuccessLog(string line)
        {
            writeToWholeLog(line, false, LogLevel.SUCCESS);
        }

        public static void writeWarningLog(string line)
        {
            writeToWholeLog(line, false, LogLevel.WARNING);
        }

        public static void writeFile(string path, List<string> result, bool append = false)
        {
            using (System.IO.StreamWriter file =
                    new System.IO.StreamWriter(path, append))
            {
                foreach (string line in result)
                {
                    file.WriteLine(line);
                }
            }
        }

        public static void writeRecord(Dictionary<string, List<string>> record)
        {
            if (string.IsNullOrEmpty(SonyImport.log_record_file))
            {
                return;
            }

            using (System.IO.StreamWriter file =
                    new System.IO.StreamWriter(SonyImport.log_record_file))
            {
                string final = JsonConvert.SerializeObject(record);
                file.WriteLine(final);
            }
        }

        public static string getCurrent()
        {
            return DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }

        public static Dictionary<string, object> checkFileExist(XmlNode file, string path)
        {
            string fp = path +
                file.SelectSingleNode("FilePath").InnerText +
                file.SelectSingleNode("FileName").InnerText;

            Dictionary<string, object> result = new Dictionary<string, object>{
                { "success" , false },
                { "path" , fp },
                { "status" , -1 },
                { "msg" , "" }
            };

            try
            {
                if (File.Exists(fp))
                {
                    if (file.SelectSingleNode("HashSum/HashSumAlgorithmType").InnerText == "MD5")
                    {
                        string md5 = file.SelectSingleNode("HashSum/HashSum").InnerText;

                        if (getMD5(fp) == md5)
                        {
                            result["success"] = true;
                            result["status"] = CustomException.FILE_SUCCESS;
                        }
                        else
                        {
                            throw new CustomException("File corrupted", CustomException.FILE_CORRUPTED);
                        }
                    }
                    else
                    {
                        throw new CustomException("Not MD5 check", CustomException.FILE_NOT_MD5_CHECK);
                    }
                }
                else
                {
                    throw new CustomException("File not exists", CustomException.FILE_NOT_FOUND);
                }

            }
            catch (CustomException ex)
            {
                result["success"] = false;
                result["status"] = ex.status;
                result["msg"] = ex.msg;
            }

            return result;

        }

        // 专门处理 Sony XML 格式的文件检查方法
        public static Dictionary<string, object> checkSonyFileExist(XmlNode file, string basePath)
        {
            writeDebugLog("checkSonyFileExist: Starting file check");

            Dictionary<string, object> result = new Dictionary<string, object>{
                { "success" , false },
                { "path" , "" },
                { "status" , -1 },
                { "msg" , "" }
            };

            try
            {
                // Sony XML 格式使用 URI 而不是 FilePath + FileName
                XmlNode uriNode = file.SelectSingleNode("URI");
                if (uriNode == null)
                {
                    throw new CustomException("Missing URI in File node", CustomException.FILE_NOT_FOUND);
                }

                string uri = uriNode.InnerText;
                writeDebugLog("checkSonyFileExist: URI = " + uri);

                // 优先检查本地文件：从URI中提取文件名
                string fileName = "";
                try
                {
                    Uri uriObj = new Uri(uri);
                    string path = uriObj.LocalPath;
                    fileName = System.IO.Path.GetFileName(path);
                    writeDebugLog("checkSonyFileExist: Extracted filename = " + fileName);
                }
                catch (Exception ex)
                {
                    writeToWholeLog("checkSonyFileExist: Failed to extract filename from URI: " + ex.Message, true);
                }

                if (!string.IsNullOrEmpty(fileName))
                {
                    string resourcesDir = System.IO.Path.Combine(basePath, "resources");
                    string localFilePath = System.IO.Path.Combine(resourcesDir, fileName);
                    writeDebugLog("checkSonyFileExist: Checking local file: " + localFilePath);

                    // 如果直接文件名不存在，尝试查找带前缀的文件
                    if (!File.Exists(localFilePath))
                    {
                        writeDebugLog("checkSonyFileExist: Direct filename not found, searching for prefixed files");

                        if (Directory.Exists(resourcesDir))
                        {
                            // 查找所有以fileName结尾的文件
                            string[] matchingFiles = Directory.GetFiles(resourcesDir, "*" + fileName);
                            if (matchingFiles.Length > 0)
                            {
                                localFilePath = matchingFiles[0]; // 使用第一个匹配的文件
                                writeToWholeLog("checkSonyFileExist: Found prefixed file: " + localFilePath);
                            }
                            else
                            {
                                writeDebugLog("checkSonyFileExist: No matching files found in resources directory");
                            }
                        }
                    }

                    if (File.Exists(localFilePath))
                    {
                        writeDebugLog("checkSonyFileExist: Local file found, checking MD5");

                        // 检查MD5校验
                        XmlNode hashNode = file.SelectSingleNode("HashSum/HashSumValue");
                        if (hashNode != null)
                        {
                            string expectedMD5 = hashNode.InnerText;
                            string actualMD5 = getMD5(localFilePath);

                            writeDebugLog("checkSonyFileExist: Expected MD5 = " + expectedMD5);
                            writeDebugLog("checkSonyFileExist: Actual MD5 = " + actualMD5);

                            if (expectedMD5.Equals(actualMD5, StringComparison.OrdinalIgnoreCase))
                            {
                                // 本地文件存在且MD5校验通过，优先使用本地文件
                                result["success"] = true;
                                result["path"] = localFilePath;
                                result["status"] = CustomException.FILE_SUCCESS;
                                result["msg"] = "Local file found and verified";
                                writeSuccessLog("checkSonyFileExist: Using local file (MD5 verified): " + localFilePath);
                                return result;
                            }
                            else
                            {
                                writeToWholeLog("checkSonyFileExist: Local file MD5 mismatch, will use remote URI", true);
                            }
                        }
                        else
                        {
                            // 没有MD5信息，直接使用本地文件
                            result["success"] = true;
                            result["path"] = localFilePath;
                            result["status"] = CustomException.FILE_SUCCESS;
                            result["msg"] = "Local file found (no MD5 check)";
                            writeSuccessLog("checkSonyFileExist: Using local file (no MD5 check): " + localFilePath);
                            return result;
                        }
                    }
                    else
                    {
                        writeToWholeLog("checkSonyFileExist: Local file not found: " + localFilePath);
                    }
                }

                // 如果本地文件不存在或校验失败，使用远程URI作为备选
                result["success"] = true;
                result["path"] = uri;
                result["status"] = CustomException.FILE_SUCCESS;
                result["msg"] = "Remote file URI found";
                writeToWholeLog("checkSonyFileExist: Using remote URI as fallback: " + uri);
            }
            catch (CustomException ex)
            {
                result["success"] = false;
                result["status"] = ex.status;
                result["msg"] = ex.msg;
                writeToWholeLog("checkSonyFileExist: Error - " + ex.msg, true);
            }
            catch (Exception ex)
            {
                result["success"] = false;
                result["status"] = CustomException.FILE_NOT_FOUND;
                result["msg"] = "Unexpected error: " + ex.Message;
                writeToWholeLog("checkSonyFileExist: Unexpected error - " + ex.Message, true);
            }

            return result;
        }

        public static string getMD5(string path)
        {
            using (var md5 = MD5.Create())
            {
                using (var stream = File.OpenRead(path))
                {
                    return BitConverter.ToString(md5.ComputeHash(stream)).Replace("-", String.Empty).ToLower();
                }
            }
        }

        public static bool uploadFileBySFTP(string fpath, string tpath)
        {
            // 优先使用系统SFTP命令，因为更稳定
            try
            {
                writeDebugLog("Trying system SFTP command first");
                if (uploadFileBySystemSFTP(fpath, tpath))
                {
                    return true;
                }
                else
                {
                    writeDebugLog("System SFTP failed, trying SSH.NET library");
                    return uploadFileBySFTPLibrary(fpath, tpath);
                }
            }
            catch (Exception ex)
            {
                writeToWholeLog("Both SFTP methods failed: " + ex.Message, true);
                return false;
            }
        }

        public static bool uploadFileBySystemSFTP(string fpath, string tpath)
        {
            try
            {
                writeDebugLog("Using system SFTP command for upload: " + fpath + " -> " + tpath);

                // 创建SFTP批处理脚本
                string scriptPath = Path.GetTempFileName();
                string remotePath = "/web3/" + tpath;
                string remoteDir = Path.GetDirectoryName(remotePath).Replace("\\", "/");

                string sftpCommands = $@"mkdir -p {remoteDir}
put {fpath} {remotePath}
quit";

                File.WriteAllText(scriptPath, sftpCommands);

                // 执行SFTP命令
                Process process = new Process();
                process.StartInfo.FileName = "sftp";
                process.StartInfo.Arguments = $"-o HostKeyAlgorithms=+ssh-rsa -o StrictHostKeyChecking=no -b {scriptPath} {AppCommon.FTPUser}@{AppCommon.FTPServer}";
                process.StartInfo.UseShellExecute = false;
                process.StartInfo.RedirectStandardOutput = true;
                process.StartInfo.RedirectStandardError = true;
                process.StartInfo.RedirectStandardInput = true;
                process.StartInfo.CreateNoWindow = true;

                process.Start();

                // 发送密码
                process.StandardInput.WriteLine(AppCommon.FTPPassword);
                process.StandardInput.Close();

                string output = process.StandardOutput.ReadToEnd();
                string error = process.StandardError.ReadToEnd();

                // 设置60秒超时
                bool exited = process.WaitForExit(60000);
                if (!exited)
                {
                    writeToWholeLog("System SFTP upload timeout, killing process", true);
                    process.Kill();
                    return false;
                }

                // 清理临时文件
                File.Delete(scriptPath);

                if (process.ExitCode == 0)
                {
                    writeSuccessLog("System SFTP upload successful");
                    return true;
                }
                else
                {
                    writeToWholeLog("System SFTP upload failed: " + error, true);
                    return false;
                }
            }
            catch (Exception ex)
            {
                writeToWholeLog("System SFTP upload error: " + ex.Message, true);
                return false;
            }
        }

        public static bool uploadFileBySFTPLibrary(string fpath, string tpath)
        {
            // 原来的SSH.NET库实现（保留作为备选）
            var connectionInfo = new PasswordConnectionInfo(AppCommon.FTPServer, 22, AppCommon.FTPUser, AppCommon.FTPPassword);
            connectionInfo.Timeout = TimeSpan.FromSeconds(60); // 增加到60秒超时

            SftpClient ftp = new SftpClient(connectionInfo);

            var fileStream = new FileStream(fpath, FileMode.Open);

            try
            {
                writeDebugLog("SFTP: Attempting to connect to " + AppCommon.FTPServer + ":22 as " + AppCommon.FTPUser);

                // 使用Thread来实现超时控制
                bool connectResult = false;
                Exception connectException = null;

                var connectThread = new System.Threading.Thread(() => {
                    try
                    {
                        ftp.Connect();
                        connectResult = true;
                    }
                    catch (Exception ex)
                    {
                        connectException = ex;
                        writeToWholeLog("SFTP Connect Exception: " + ex.Message, true);
                    }
                });

                connectThread.Start();

                // 等待连接完成，最多60秒
                if (connectThread.Join(60000) && connectResult)
                {
                    writeSuccessLog("SFTP: Connected successfully");

                    if (ftp.IsConnected)
                    {
                        CreateDirectoryRecursively(ftp, "/web3/" + tpath);

                        if (fileStream != null)
                        {
                            writeDebugLog("SFTP: Uploading file to /web3/" + tpath);
                            ftp.UploadFile(fileStream, "/web3/" + tpath, null);
                            writeSuccessLog("SFTP: File uploaded successfully");
                        }
                        else
                        {
                            throw new Exception("File Not Found.");
                        }
                    }
                    else
                    {
                        writeToWholeLog("SFTP: Connection established but not connected", true);
                        return false;
                    }
                }
                else
                {
                    writeToWholeLog("SFTP: Connection timeout or failed", true);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                writeToWholeLog("SFTP ERROR : " + ex.Message, true);
                writeToWholeLog("SFTP ERROR Details: " + ex.ToString(), true);
            }
            finally
            {
                if (fileStream != null)
                {
                    fileStream.Close();
                }
                ftp.Disconnect();
                ftp.Dispose();
            }
            return false;
        }

        private static void CreateDirectoryRecursively(SftpClient client, string path)
        {
            string current = "";

            if (path[0] == '/')
            {
                path = path.Substring(1);
            }

            while (!string.IsNullOrEmpty(path))
            {
                int p = path.IndexOf('/');
                current += '/';
                if (p >= 0)
                {
                    current += path.Substring(0, p);
                    path = path.Substring(p + 1);
                }
                else if (p == path.LastIndexOf('/'))
                {
                    return;
                }
                else
                {
                    current += path;
                    path = "";
                }

                try
                {
                    SftpFileAttributes attrs = client.GetAttributes(current);
                    if (!attrs.IsDirectory)
                    {
                        throw new Exception("not directory");
                    }
                }
                catch (SftpPathNotFoundException)
                {
                    client.CreateDirectory(current);
                }
            }
        }

        public static bool checkPriority(string olds, string news)
        {
            // if new lang code is null , must be false
            if (string.IsNullOrEmpty(news))
            {
                return false;
            }

            string ub = news.ToUpper();

            // if old lang code is null , check if the new is in array
            if (string.IsNullOrEmpty(olds))
            {
                return langOrder.ContainsKey(ub);
            }

            string ua = olds.ToUpper();

            if (!langOrder.ContainsKey(ua))
            {
                return langOrder.ContainsKey(ub);
            }

            if (!langOrder.ContainsKey(ub))
            {
                return false;
            }

            return langOrder[ua] > langOrder[ub];

        }

        public static string execute(string argu)
        {
            Process cmd = new Process();

            // 根据操作系统选择正确的ffmpeg可执行文件名
            string ffmpegExecutable = Environment.OSVersion.Platform == PlatformID.Win32NT ? "ffmpeg.exe" : "ffmpeg";
            cmd.StartInfo.FileName = ffmpegExecutable;
            cmd.StartInfo.Arguments = argu;
            cmd.StartInfo.RedirectStandardOutput = true;
            cmd.StartInfo.RedirectStandardError = true;
            cmd.StartInfo.CreateNoWindow = true;
            cmd.StartInfo.UseShellExecute = false;

            string output = "";

            if (!cmd.Start())
            {
                Console.WriteLine("Error starting");
                return output;
            }

            StreamReader reader = cmd.StandardError;
            output = reader.ReadToEnd();

            cmd.WaitForExit();
            cmd.Close();
            cmd.Dispose();
            reader.Close();
            reader.Dispose();

            return output;
        }

        public static string dirname(string path)
        {
            FileInfo info = new FileInfo(path);
            return info.DirectoryName;
        }

        public static List<object> getInsertSql(string table, Dictionary<string, object> p)
        {
            List<object> result = new List<object>();
            string sql = "insert into " + table + ' ';
            Dictionary<string, object> para = new Dictionary<string, object>();
            string[] keys = p.Keys.ToArray();
            object[] values = p.Values.ToArray();
            List<string> bindKeys = new List<string>();

            string _b = "@A";
            int count = 1;
            foreach (object o in values)
            {
                string _k = _b + (count++);
                para.Add(_k, o);
                bindKeys.Add(_k);
            }

            sql += "(" + string.Join(",", keys) + ") values (" + string.Join(",", bindKeys.ToArray()) + ")";

            result.Add(sql);
            result.Add(para);
            return result;
        }

        public static List<object> getUpdateSql(string table, Dictionary<string, object> f, Dictionary<string, object> w )
        {
            List<object> result = new List<object>();
            string sql = "update " + table + " set ";
            Dictionary<string, object> para = new Dictionary<string, object>();

            List<string> bindUpdate = new List<string>();
            List<string> bindWhere = new List<string>();

            string _b = "@A";
            int count = 1;
            foreach (KeyValuePair<string , object> o in f)
            {
                string _k = _b + (count++);
                para.Add(_k, o.Value);
                bindUpdate.Add(o.Key + '=' + _k);
            }

            foreach (KeyValuePair<string, object> o in w)
            {
                string _k = _b + (count++);
                para.Add(_k, o.Value);
                bindWhere.Add(o.Key + '=' + _k);
            }

            sql += string.Join("," , bindUpdate) + " where " + string.Join(" and " , bindWhere);

            result.Add(sql);
            result.Add(para);
            return result;
        }

        public static bool uploadFile(string localPath, string remotePath)
        {
            try
            {
                writeToWholeLog("Starting file upload: " + localPath + " -> " + remotePath);

                // 使用SFTP上传文件
                bool result = uploadFileBySFTP(localPath, remotePath);

                if (result)
                {
                    writeSuccessLog("File upload successful: " + remotePath);
                }
                else
                {
                    writeToWholeLog("File upload failed: " + remotePath, true);
                }

                return result;
            }
            catch (Exception ex)
            {
                writeToWholeLog("Upload file error: " + ex.Message, true);
                return false;
            }
        }

        public static Dictionary<string, object> downloadRemoteFile(string remoteUrl, string tempDir)
        {
            Dictionary<string, object> result = new Dictionary<string, object>
            {
                { "success", false },
                { "localPath", "" },
                { "error", "" }
            };

            try
            {
                writeToWholeLog("Starting remote file download: " + remoteUrl);

                // 创建临时目录
                if (!Directory.Exists(tempDir))
                {
                    Directory.CreateDirectory(tempDir);
                }

                // 从URL获取文件名
                string fileName = Path.GetFileName(new Uri(remoteUrl).LocalPath);
                if (string.IsNullOrEmpty(fileName))
                {
                    fileName = "downloaded_file_" + DateTime.Now.Ticks;
                }

                string localPath = Path.Combine(tempDir, fileName);

                // 使用WebClient下载文件
                using (var client = new System.Net.WebClient())
                {
                    client.DownloadFile(remoteUrl, localPath);
                }

                if (File.Exists(localPath))
                {
                    result["success"] = true;
                    result["localPath"] = localPath;
                    writeToWholeLog("Remote file download successful: " + localPath);
                }
                else
                {
                    result["error"] = "Downloaded file not found";
                    writeToWholeLog("Downloaded file not found: " + localPath, true);
                }
            }
            catch (Exception ex)
            {
                result["error"] = ex.Message;
                writeToWholeLog("Download remote file error: " + ex.Message, true);
            }

            return result;
        }
    }
}
