﻿using System;
using System.Collections.Generic;
using MySql.Data.MySqlClient;
using System.Data;
using Sony_Song_Import.util;

namespace Sony_Song_Import
{
    class DB
    {
        public static long SUCCESS = 1;
        public static long FAILURE = 0;

        public static DB shared = getInstance();

        private static DB singleTon;

        MySqlConnectionStringBuilder myBuilder;

        private DB()
        {
            myBuilder = new MySqlConnectionStringBuilder();
            myBuilder.Database = util.AppCommon.SQLDB;
            myBuilder.Server = util.AppCommon.SQLServer;
            myBuilder.Port = (uint)util.AppCommon.SQLPort;
            myBuilder.UserID = util.AppCommon.SQLUser;
            myBuilder.Password = util.AppCommon.SQLPassword;
            myBuilder.ConnectionTimeout = 30; // 30秒连接超时
            myBuilder.DefaultCommandTimeout = 60; // 60秒命令超时
        }

        private MySqlConnection getConn()
        {
            Util.writeDebugLog("DB.getConn: Creating new MySQL connection");
            MySqlConnection conn = new MySqlConnection();
            conn.ConnectionString = myBuilder.ConnectionString;

            try
            {
                Util.writeDebugLog("DB.getConn: About to open connection to " + myBuilder.Server + ":" + myBuilder.Port);
                conn.Open();
                Util.writeDebugLog("DB.getConn: Connection opened successfully");
            }
            catch (Exception ex)
            {
                Util.writeToWholeLog("SQL Connect ERROR : " + ex.Message, true); // Log the detailed error
                Util.writeToWholeLog("Connection string: " + myBuilder.ConnectionString, true);
                throw; // Re-throw the original exception to preserve details
            }

            return conn;
        }

        private static DB getInstance()
        {
            if (singleTon == null)
            {
                singleTon = new DB();
            }
            return singleTon;
        }

        // we need to lock before the reader is close due to single instance
        public void select(string query , Dictionary<string , object> para = null , Action<MySqlDataReader> fn = null)
        {
            Util.writeDebugLog("DB.select: Starting query: " + query);
            using (var connection = getConn())
            {
                Util.writeDebugLog("DB.select: Connection obtained, creating command");
                using (MySqlCommand cmd = getCommand(query, connection))
                {
                    Util.writeDebugLog("DB.select: Command created, binding parameters");
                    cmd.bind(para);

                    Util.writeDebugLog("DB.select: Parameters bound, executing reader");
                    using (var reader = cmd.ExecuteReader())
                    {
                        Util.writeDebugLog("DB.select: Reader created, calling callback function");
                        fn(reader);
                        Util.writeDebugLog("DB.select: Callback function completed");
                    }
                    Util.writeDebugLog("DB.select: Reader disposed");
                }
                Util.writeDebugLog("DB.select: Command disposed");
            }
            Util.writeDebugLog("DB.select: Connection disposed, query completed");
        }

        public Dictionary<string , long> query(string query , Dictionary<string, object> para = null)
        {
            Dictionary<string, long> result = new Dictionary<string, long>
            {
                { "success" , FAILURE } ,
                { "inert_row_id" , -1 }
            };

            using (var connection = getConn())
            {
                using (MySqlCommand cmd = getCommand(query, connection))
                {

                    cmd.bind(para);

                    try
                    {
                        cmd.ExecuteNonQuery();
                        result["success"] = SUCCESS;
                        result["inert_row_id"] = cmd.LastInsertedId;
                    }
                    catch (Exception ex)
                    {
                        result["success"] = FAILURE;
                        result["inert_row_id"] = -1;
                        Util.writeToWholeLog("DB Query Error : " + ex.Message, true);
                        Util.writeToWholeLog("DB Query Error : " + query, true);
                    }

                    return result;
                }
            }
        }

        private MySqlCommand getCommand(string query , MySqlConnection conn)
        {
            MySqlCommand cmd = new MySqlCommand();
            cmd.CommandText = query;

            cmd.CommandType = CommandType.Text;
            cmd.Connection = conn;

            return cmd;
        }

        
    }
}
