using System;
using System.IO;
using Renci.SshNet;

class SimpleSFTPUploadTest
{
    static void Main(string[] args)
    {
        Console.WriteLine("Simple SFTP Upload Test...");
        
        string server = "192.168.2.53";
        string username = "hkria";
        string password = "1";
        int port = 22;
        
        try
        {
            // 创建测试文件
            string testFile = "sftp_upload_test.txt";
            File.WriteAllText(testFile, $"SFTP Upload Test - {DateTime.Now}");
            Console.WriteLine($"Created test file: {testFile}");
            
            // 创建连接信息
            var connectionInfo = new PasswordConnectionInfo(server, port, username, password);
            connectionInfo.Timeout = TimeSpan.FromSeconds(30);
            
            using (var sftp = new SftpClient(connectionInfo))
            {
                Console.WriteLine($"Connecting to {server}:{port} as {username}...");
                sftp.Connect();
                
                if (sftp.IsConnected)
                {
                    Console.WriteLine("✅ Connected successfully!");
                    
                    // 创建远程目录（如果不存在）
                    string remoteDir = "/web3/test";
                    try
                    {
                        if (!sftp.Exists(remoteDir))
                        {
                            Console.WriteLine($"Creating remote directory: {remoteDir}");
                            sftp.CreateDirectory(remoteDir);
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Warning: Could not create directory {remoteDir}: {ex.Message}");
                    }
                    
                    // 上传测试文件
                    string remotePath = "/web3/test/sftp_upload_test.txt";
                    using (var fileStream = new FileStream(testFile, FileMode.Open))
                    {
                        Console.WriteLine($"Uploading {testFile} to {remotePath}...");
                        sftp.UploadFile(fileStream, remotePath);
                        Console.WriteLine("✅ Upload successful!");
                    }
                    
                    // 验证文件是否存在
                    if (sftp.Exists(remotePath))
                    {
                        Console.WriteLine("✅ File verified on remote server!");
                        
                        // 获取文件信息
                        var fileInfo = sftp.GetAttributes(remotePath);
                        Console.WriteLine($"Remote file size: {fileInfo.Size} bytes");
                        Console.WriteLine($"Remote file modified: {fileInfo.LastWriteTime}");
                    }
                    else
                    {
                        Console.WriteLine("❌ File not found on remote server!");
                    }
                    
                    sftp.Disconnect();
                    Console.WriteLine("Disconnected from server.");
                }
                else
                {
                    Console.WriteLine("❌ Failed to connect!");
                }
            }
            
            // 清理测试文件
            if (File.Exists(testFile))
            {
                File.Delete(testFile);
                Console.WriteLine("Local test file cleaned up.");
            }
            
            Console.WriteLine("✅ SFTP Upload Test completed successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error: {ex.Message}");
            Console.WriteLine($"Details: {ex}");
        }
        
        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
}
