@echo off
chcp 65001 >nul
title Sony Music Import System

echo ========================================
echo Sony Music Import System
echo ========================================
echo.

REM 检查参数
if "%1"=="" (
    echo [ERROR] Missing date parameter!
    echo.
    echo Usage: run_sony_import.bat [DATE]
    echo Example: run_sony_import.bat 20240829
    echo.
    echo The DATE should match a folder in data/sony/[DATE]/
    echo.
    pause
    exit /b 1
)

REM 检查exe文件是否存在
if not exist "Sony Song Import.exe" (
    echo [ERROR] Sony Song Import.exe not found!
    echo Please make sure you are running this batch file in the correct directory.
    echo.
    pause
    exit /b 1
)

REM 检查配置文件
if not exist "sony.xml" (
    echo [ERROR] Configuration file sony.xml not found!
    echo Please make sure sony.xml is in the same directory as the exe file.
    echo.
    pause
    exit /b 1
)

REM 检查数据目录
if not exist "data\sony\%1" (
    echo [WARNING] Data directory data\sony\%1 not found!
    echo Please make sure the data directory exists before running.
    echo.
    echo Expected directory structure:
    echo   data\sony\%1\
    echo     ^-- [Package directories with XML files]
    echo.
    set /p continue="Continue anyway? (y/N): "
    if /i not "%continue%"=="y" (
        echo Operation cancelled.
        pause
        exit /b 1
    )
    echo.
)

REM 创建日志目录
if not exist "logs" mkdir logs
if not exist "logs\%1" mkdir "logs\%1"

echo [INFO] Starting Sony Import for date: %1
echo [INFO] Log files will be saved to: logs\%1\
echo [INFO] Press Ctrl+C to stop the program if needed
echo.
echo ========================================
echo.

REM 运行程序
"Sony Song Import.exe" %1

REM 检查程序退出代码
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo [SUCCESS] Program completed successfully!
) else (
    echo.
    echo ========================================
    echo [ERROR] Program exited with error code: %ERRORLEVEL%
)

echo.
echo Check the log file for detailed information:
if exist "logs\%1\log.txt" (
    echo   logs\%1\log.txt
) else (
    echo   [WARNING] Log file not found - there may have been an error
)

echo.
echo ========================================
pause
