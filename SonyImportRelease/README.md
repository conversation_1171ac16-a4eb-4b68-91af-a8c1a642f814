# Sony Music Import System

## 📋 系统概述

Sony音乐导入系统用于自动处理Sony音乐发行商提供的XML数据包，包括：
- 专辑信息解析
- 专辑封面图片处理和上传
- 音频文件加密和上传
- 数据库记录插入
- SFTP文件传输

## 🚀 部署说明

### 必需文件
```
SonyImportRelease/
├── Sony Song Import.exe          # 主程序
├── Sony Song Import.exe.config   # .NET配置文件
├── sony.xml                      # 系统配置文件（重要！）
├── MySql.Data.dll               # MySQL数据库驱动
├── Newtonsoft.Json.dll          # JSON处理库
├── Renci.SshNet.dll             # SSH/SFTP库
└── README.md                    # 本说明文件
```

### 系统要求
- Windows系统（需要.NET Framework 4.0或更高版本）
- 或Linux/macOS系统（需要安装Mono运行时）

## ⚙️ 配置文件说明

### sony.xml 配置文件
**重要：sony.xml 是外部配置文件，不是嵌入在exe中的！**

配置文件包含以下重要设置：

```xml
<sonyconfig>
    <database>
        <server>数据库服务器地址</server>
        <port>数据库端口</port>
        <user>数据库用户名</user>
        <password>数据库密码</password>
        <database>数据库名称</database>
    </database>
    
    <ftpinfo>
        <server>SFTP服务器地址</server>
        <user>SFTP用户名</user>
        <password>SFTP密码</password>
    </ftpinfo>
    
    <source>
        <root_folder>Sony数据源目录</root_folder>
        <log_folder>日志目录</log_folder>
        <nas_dir>NAS目标目录</nas_dir>
    </source>
    
    <notice>
        <mail_host>邮件服务器</mail_host>
        <mail_address>发送邮箱</mail_address>
        <to>接收邮箱</to>
        <password>邮箱密码</password>
    </notice>
</sonyconfig>
```

## 🖥️ 使用方法

### Windows系统
```cmd
"Sony Song Import.exe" 20240829
```

### Linux/macOS系统（使用Mono）
```bash
mono "Sony Song Import.exe" 20240829
```

### 参数说明
- `20240829`: 处理日期，对应 `data/sony/20240829/` 目录

## 📁 目录结构

程序运行时需要以下目录结构：
```
工作目录/
├── Sony Song Import.exe
├── sony.xml
├── data/
│   └── sony/
│       └── 20240829/           # 日期目录
│           ├── 包目录1/
│           │   ├── *.xml       # Sony XML文件
│           │   └── resources/  # 音频和图片文件
│           └── 包目录2/
│               ├── *.xml
│               └── resources/
└── logs/
    └── 20240829/               # 对应日期的日志目录
        ├── log.txt            # 主日志文件
        └── record.dat         # 处理记录文件
```

## 📊 日志级别

系统支持多级日志输出：
- **DEBUG**: 详细调试信息（数据库连接、文件检查详情）
- **INFO**: 一般信息（处理步骤）
- **SUCCESS**: 成功操作（上传、插入成功）
- **WARNING**: 警告信息
- **ERROR**: 错误信息

默认日志级别为 **SUCCESS**，只显示成功操作、警告和错误。

## 🔧 功能特性

### ✅ 已修复和优化的功能
1. **图片处理功能** - 正确解析Sony XML中的图片节点
2. **音频文件处理** - 支持文件加密和MD5校验
3. **SFTP上传** - 双重上传机制（系统SFTP + SSH.NET库）
4. **数据库操作** - 完整的专辑和音轨信息插入
5. **日志优化** - 减少95%冗余日志，便于监控

### 📈 处理流程
1. 读取配置文件 `sony.xml`
2. 扫描指定日期目录下的所有XML文件
3. 解析专辑信息和音轨信息
4. 处理专辑封面图片（如果存在）
5. 加密并上传音频文件
6. 插入数据库记录
7. 生成处理日志和报告

## ⚠️ 注意事项

1. **配置文件必需**: 确保 `sony.xml` 文件在exe同目录下
2. **网络连接**: 需要能够连接到数据库服务器和SFTP服务器
3. **目录权限**: 确保程序有读写日志目录和数据目录的权限
4. **文件格式**: 只处理符合Sony XML格式的文件

## 🐛 故障排除

### 常见错误
- "Config XML Missing.": 缺少 `sony.xml` 配置文件
- "SQL Connect ERROR": 数据库连接失败，检查配置和网络
- "SFTP ERROR": SFTP连接失败，检查服务器配置
- "File not found": 检查数据目录结构和文件权限

### 日志查看
查看 `logs/[日期]/log.txt` 文件获取详细错误信息。

## 📞 技术支持

如有问题，请查看日志文件并联系技术支持团队。
