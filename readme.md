xbuild "Sony Song Import.csproj"   && mono "bin/Debug/Sony Song Import.exe" 20240829


DELETE FROM `avs`.`label_link` WHERE `track_id` > 4249947;
DELETE FROM `avs`.`m3_album_link` WHERE `track_id` > 4249947;
DELETE FROM `avs`.`m3_artist_link` WHERE `track_id` > 4249947;
DELETE FROM `avs`.`track_name` WHERE `track_id` > 4249947;
DELETE FROM `avs`.`track` WHERE `track_id` > 4249947;

1. 从 XML 获取文件路径/URL
2. 判断是否为远程 URL
   ├─ 如果是远程 URL → 验证有效性 → 下载到本地，下载失败则
   └─ 如果是本地路径 → 直接使用本地文件
3. 使用 ffmpeg 处理本地文件获取技术参数
4. 加密本地文件
5. 上传加密文件到 SFTP
6. 设置 filelocation 字段
7. 删除临时文件


slionx@chunqiufangxingwuyeheilongwenAir Sony Song Import % sftp -o HostKeyAlgorithms=+ssh-rsa hkria@************
The authenticity of host '************ (************)' can't be established.
ED25519 key fingerprint is SHA256:lQR2YtTeqi9e4Kac+va/CjCWDBpkTVujo6xV0XvjWfY.
This key is not known by any other names.
Are you sure you want to continue connecting (yes/no/[fingerprint])? yes
Warning: Permanently added '************' (ED25519) to the list of known hosts.
hkria@************'s password: 
Connected to ************.
sftp> 



# ===== 网络基础配置 =====
Port 22
AddressFamily any
ListenAddress 0.0.0.0
ListenAddress ::

# ===== 密钥文件路径 =====
HostKey __PROGRAMDATA__/ssh/ssh_host_rsa_key
HostKey __PROGRAMDATA__/ssh/ssh_host_ecdsa_key
HostKey __PROGRAMDATA__/ssh/ssh_host_ed25519_key

# ===== SSH.NET 2016.0.0 兼容性配置 =====
KexAlgorithms diffie-hellman-group1-sha1,diffie-hellman-group14-sha1,diffie-hellman-group-exchange-sha1,diffie-hellman-group-exchange-sha256,diffie-hellman-group16-sha512,diffie-hellman-group18-sha512,diffie-hellman-group14-sha256,ecdh-sha2-nistp256,ecdh-sha2-nistp384,ecdh-sha2-nistp521,curve25519-sha256,<EMAIL>

HostKeyAlgorithms ssh-rsa,ssh-dss,rsa-sha2-512,rsa-sha2-256,ssh-ed25519,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521

Ciphers aes128-cbc,aes192-cbc,aes256-cbc,3des-cbc,aes128-ctr,aes192-ctr,aes256-ctr,<EMAIL>,<EMAIL>,<EMAIL>

MACs hmac-sha1,hmac-md5,hmac-sha2-256,hmac-sha2-512,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

# 降低RSA密钥长度要求
RequiredRSASize 1024

# ===== 认证核心配置 =====
PasswordAuthentication yes
PermitEmptyPasswords yes
PermitRootLogin yes
ChallengeResponseAuthentication no
GSSAPIAuthentication no
PubkeyAuthentication yes

# ===== 连接控制 =====
LoginGraceTime 30
MaxAuthTries 10
MaxSessions 100
ClientAliveInterval 0
ClientAliveCountMax 3

# ===== 权限与日志 =====
StrictModes no
UsePAM no
LogLevel INFO
SyslogFacility AUTH

# ===== 功能开关 =====
AllowAgentForwarding yes
AllowTcpForwarding yes
GatewayPorts yes
X11Forwarding yes
PrintMotd yes
PrintLastLog yes
TCPKeepAlive yes

# ===== 子系统配置 =====
Subsystem sftp sftp-server.exe
AuthorizedKeysFile .ssh/authorized_keys