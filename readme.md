xbuild "Sony Song Import.csproj"   && mono "bin/Debug/Sony Song Import.exe" 20240829


# 数据库记录删除和SFTP上传验证流程

## 问题背景
Sony音乐导入系统使用SSH.NET 2016.0.0库进行SFTP上传，但遇到了兼容性问题。通过配置SSH服务器算法成功解决了此问题。

## SSH服务器配置修复
在Windows SSH服务器的配置文件中添加了SSH.NET 2016兼容的算法：

```bash
# SSH.NET 2016.0.0 兼容性配置
KexAlgorithms diffie-hellman-group1-sha1,diffie-hellman-group14-sha1,diffie-hellman-group-exchange-sha1,diffie-hellman-group-exchange-sha256,diffie-hellman-group16-sha512,diffie-hellman-group18-sha512,diffie-hellman-group14-sha256,ecdh-sha2-nistp256,ecdh-sha2-nistp384,ecdh-sha2-nistp521,curve25519-sha256,<EMAIL>

HostKeyAlgorithms ssh-rsa,rsa-sha2-512,rsa-sha2-256,ssh-ed25519,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521

Ciphers aes128-cbc,aes192-cbc,aes256-cbc,3des-cbc,aes128-ctr,aes192-ctr,aes256-ctr,<EMAIL>,<EMAIL>,<EMAIL>

MACs hmac-sha1,hmac-md5,hmac-sha2-256,hmac-sha2-512,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

RequiredRSASize 1024
```

## SFTP上传验证流程

### 1. 查找需要删除的Track ID
```bash
# 从日志中提取已插入的track_id
grep "Track Inserted" logs/20240829/log.txt | sed 's/.*Track Inserted : \([0-9]*\).*/\1/' | sort -n
```

### 2. 删除数据库记录
使用以下SQL语句删除测试记录（按需修改track_id范围）：

```sql
-- 删除关联表记录
DELETE FROM `avs`.`label_link` WHERE `track_id` > 4249947;
DELETE FROM `avs`.`m3_album_link` WHERE `track_id` > 4249947;
DELETE FROM `avs`.`m3_artist_link` WHERE `track_id` > 4249947;
DELETE FROM `avs`.`track_name` WHERE `track_id` > 4249947;

-- 删除主表记录
DELETE FROM `avs`.`track` WHERE `track_id` > 4249947;

-- 验证删除结果
SELECT COUNT(*) as remaining_tracks FROM `avs`.`track` WHERE `track_id` > 4249947;
```

### 3. 清理处理记录
```bash
# 删除处理记录文件，强制重新处理
rm logs/20240829/record.dat
```

### 4. 运行验证测试
```bash
# 运行Sony音乐导入系统
mono "bin/Debug/Sony Song Import.exe" 20240829
```

### 5. 验证结果
成功的SFTP上传会在日志中显示：
- `Track Inserted` (而不是 `Track Updated`)
- `SFTP: Attempting to connect to ************:22 as hkria`
- `SFTP: Connected successfully`
- `SFTP: Uploading file to /web3/SONY/...`
- `SFTP: File uploaded successfully`
- `Upload Track Success`

## SFTP上传路径结构
```
/web3/SONY/{日期}/{专辑目录}/{加密文件名}
```

示例：
```
/web3/SONY/20240829/N_A10301A0004750949S_20240829191151688/A10301A0004750949S_70045247.20122.bin
```

## 验证成功标志
1. ✅ SSH.NET 2016.0.0连接成功
2. ✅ 文件加密正常（生成.bin文件）
3. ✅ SFTP上传成功
4. ✅ 数据库记录插入成功
5. ✅ 完整的处理流程无错误

## 注意事项
- 只有新插入的曲目才会触发文件上传
- 已存在的曲目只会更新数据库记录，不会重新上传文件
- 删除数据库记录时需要按照外键依赖顺序进行
- SSH服务器配置修改后需要重启SSH服务

1. 从 XML 获取文件路径/URL
2. 判断是否为远程 URL
   ├─ 如果是远程 URL → 验证有效性 → 下载到本地，下载失败则
   └─ 如果是本地路径 → 直接使用本地文件
3. 使用 ffmpeg 处理本地文件获取技术参数
4. 加密本地文件
5. 上传加密文件到 SFTP
6. 设置 filelocation 字段
7. 删除临时文件


slionx@chunqiufangxingwuyeheilongwenAir Sony Song Import % sftp -o HostKeyAlgorithms=+ssh-rsa hkria@************
The authenticity of host '************ (************)' can't be established.
ED25519 key fingerprint is SHA256:lQR2YtTeqi9e4Kac+va/CjCWDBpkTVujo6xV0XvjWfY.
This key is not known by any other names.
Are you sure you want to continue connecting (yes/no/[fingerprint])? yes
Warning: Permanently added '************' (ED25519) to the list of known hosts.
hkria@************'s password: 
Connected to ************.
sftp> 



Test-Path "C:\ProgramData\ssh\sshd_config"

sshd -T | Select-String -Pattern "(kexalgorithms|hostkeyalgorithms|ciphers|macs)"