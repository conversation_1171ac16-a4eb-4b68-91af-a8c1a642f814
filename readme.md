xbuild "Sony Song Import.csproj"   && mono "bin/Debug/Sony Song Import.exe" 20240829


DELETE FROM `avs`.`label_link` WHERE `track_id` > 4249947;
DELETE FROM `avs`.`m3_album_link` WHERE `track_id` > 4249947;
DELETE FROM `avs`.`m3_artist_link` WHERE `track_id` > 4249947;
DELETE FROM `avs`.`track_name` WHERE `track_id` > 4249947;
DELETE FROM `avs`.`track` WHERE `track_id` > 4249947;

1. 从 XML 获取文件路径/URL
2. 判断是否为远程 URL
   ├─ 如果是远程 URL → 验证有效性 → 下载到本地，下载失败则
   └─ 如果是本地路径 → 直接使用本地文件
3. 使用 ffmpeg 处理本地文件获取技术参数
4. 加密本地文件
5. 上传加密文件到 SFTP
6. 设置 filelocation 字段
7. 删除临时文件


slionx@chunqiufangxingwuyeheilongwenAir Sony Song Import % sftp -o HostKeyAlgorithms=+ssh-rsa hkria@************
The authenticity of host '************ (************)' can't be established.
ED25519 key fingerprint is SHA256:lQR2YtTeqi9e4Kac+va/CjCWDBpkTVujo6xV0XvjWfY.
This key is not known by any other names.
Are you sure you want to continue connecting (yes/no/[fingerprint])? yes
Warning: Permanently added '************' (ED25519) to the list of known hosts.
hkria@************'s password: 
Connected to ************.
sftp> 



Test-Path "C:\ProgramData\ssh\sshd_config"

sshd -T | Select-String -Pattern "(kexalgorithms|hostkeyalgorithms|ciphers|macs)"