# Windows使用说明

## 🚀 快速开始

### 方法1：使用批处理文件（推荐）

1. **双击运行** `run_sony_import.bat`
2. **按提示输入日期参数**，或者
3. **拖拽方式**：将日期作为参数拖到批处理文件上

### 方法2：命令行运行

1. **打开命令提示符**：
   - 按 `Win + R`，输入 `cmd`，回车
   - 或在文件夹中按住 `Shift` + 右键，选择"在此处打开命令窗口"

2. **运行程序**：
   ```cmd
   "Sony Song Import.exe" 20240829
   ```

## 📁 目录结构

确保你的目录结构如下：
```
SonyImportRelease/
├── Sony Song Import.exe          # 主程序
├── sony.xml                      # 配置文件（必需）
├── run_sony_import.bat           # Windows批处理文件
├── MySql.Data.dll               # 依赖库
├── Newtonsoft.Json.dll          # 依赖库
├── Renci.SshNet.dll             # 依赖库
├── data/                        # 数据目录
│   └── sony/
│       └── 20240829/           # 日期目录
│           ├── 包目录1/
│           └── 包目录2/
└── logs/                       # 日志目录（自动创建）
    └── 20240829/
        └── log.txt
```

## ⚙️ 配置文件

**重要**：首次使用前必须配置 `sony.xml` 文件！

1. **复制模板**：
   ```cmd
   copy sony.xml.template sony.xml
   ```

2. **编辑配置**：用记事本打开 `sony.xml`，修改：
   - 数据库服务器信息
   - SFTP服务器信息
   - 邮件配置（可选）

## 🐛 常见问题

### 问题1：程序一闪而过
**解决方案**：使用 `run_sony_import.bat` 批处理文件运行

### 问题2：找不到配置文件
**错误信息**：`Config XML Missing.`
**解决方案**：确保 `sony.xml` 文件在exe同目录下

### 问题3：数据库连接失败
**错误信息**：`SQL Connect ERROR`
**解决方案**：检查 `sony.xml` 中的数据库配置

### 问题4：SFTP连接失败
**错误信息**：`SFTP ERROR`
**解决方案**：检查网络连接和SFTP服务器配置

### 问题5：缺少依赖库
**错误信息**：`Could not load file or assembly`
**解决方案**：确保所有DLL文件在exe同目录下

## 📊 日志查看

程序运行后，查看日志文件了解详细信息：
- **日志位置**：`logs\[日期]\log.txt`
- **用记事本打开**：`notepad logs\20240829\log.txt`

## 🔧 高级用法

### 启用详细日志
修改 `util/Util.cs` 中的日志级别：
```csharp
public static LogLevel CurrentLogLevel = LogLevel.DEBUG;  // 显示所有日志
```

### 禁用并行处理
修改 `sony.xml` 配置：
```xml
<enable_parallel>false</enable_parallel>
```

### 批量处理多个日期
创建批处理脚本：
```batch
@echo off
for %%d in (20240829 20240830 20240831) do (
    echo Processing date: %%d
    "Sony Song Import.exe" %%d
    echo.
)
pause
```

## 📞 技术支持

如遇问题：
1. 查看日志文件获取详细错误信息
2. 检查配置文件是否正确
3. 确认网络连接正常
4. 验证数据目录结构正确
