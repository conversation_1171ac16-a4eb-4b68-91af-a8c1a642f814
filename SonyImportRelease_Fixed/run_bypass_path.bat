@echo off
chcp 65001 >nul
title Sony Import - Path Bypass Mode

echo ========================================
echo Sony Import - Path Bypass Mode
echo ========================================
echo.

if "%1"=="" (
    echo Usage: run_bypass_path.bat [DATE]
    echo Example: run_bypass_path.bat 20240901
    pause
    exit /b 1
)

set DATE_PARAM=%1
set SIMPLE_DIR=C:\SonyTemp

echo [INFO] Creating temporary simple directory...
if exist "%SIMPLE_DIR%" rmdir /s /q "%SIMPLE_DIR%"
mkdir "%SIMPLE_DIR%"

echo [INFO] Copying program files...
copy "Sony Song Import.exe" "%SIMPLE_DIR%\" >nul
copy "sony.xml" "%SIMPLE_DIR%\" >nul
copy "*.dll" "%SIMPLE_DIR%\" >nul
copy "*.config" "%SIMPLE_DIR%\" >nul

echo [INFO] Creating data structure...
mkdir "%SIMPLE_DIR%\data"
mkdir "%SIMPLE_DIR%\data\sony"
mkdir "%SIMPLE_DIR%\data\sony\%DATE_PARAM%"
mkdir "%SIMPLE_DIR%\logs"
mkdir "%SIMPLE_DIR%\logs\%DATE_PARAM%"

echo [INFO] Copying data files...
if exist "data\sony\%DATE_PARAM%" (
    xcopy "data\sony\%DATE_PARAM%" "%SIMPLE_DIR%\data\sony\%DATE_PARAM%\" /E /Y >nul
    echo [OK] Data files copied
) else (
    echo [WARNING] No data files found for %DATE_PARAM%
)

echo [INFO] Changing to simple directory: %SIMPLE_DIR%
cd /d "%SIMPLE_DIR%"

echo [INFO] Current directory: %CD%
echo [INFO] Running Sony Import...
echo.

REM 设置环境变量强制使用简单路径
set TEMP=%SIMPLE_DIR%\temp
set TMP=%SIMPLE_DIR%\temp
mkdir "%SIMPLE_DIR%\temp" 2>nul

REM 运行程序
"%SIMPLE_DIR%\Sony Song Import.exe" %DATE_PARAM%

set EXIT_CODE=%ERRORLEVEL%

echo.
echo [INFO] Program finished with exit code: %EXIT_CODE%

echo [INFO] Copying logs back to original location...
if exist "logs\%DATE_PARAM%\log.txt" (
    if not exist "%~dp0logs" mkdir "%~dp0logs"
    if not exist "%~dp0logs\%DATE_PARAM%" mkdir "%~dp0logs\%DATE_PARAM%"
    copy "logs\%DATE_PARAM%\*" "%~dp0logs\%DATE_PARAM%\" >nul
    echo [OK] Logs copied back
    
    echo.
    echo [INFO] Last 20 lines of log:
    echo ----------------------------------------
    powershell "Get-Content 'logs\%DATE_PARAM%\log.txt' | Select-Object -Last 20"
    echo ----------------------------------------
) else (
    echo [WARNING] No log file generated
)

echo.
echo [INFO] Cleaning up temporary directory...
cd /d "%~dp0"
rmdir /s /q "%SIMPLE_DIR%" 2>nul

echo.
echo ========================================
if %EXIT_CODE% equ 0 (
    echo [SUCCESS] Program completed successfully!
) else (
    echo [ERROR] Program failed with exit code: %EXIT_CODE%
)
echo ========================================
pause
