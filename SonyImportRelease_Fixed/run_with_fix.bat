@echo off
chcp 65001 >nul
title Sony Import with Windows Path Fix

echo ========================================
echo Sony Import with Windows Path Fix
echo ========================================
echo.

REM 检查参数
if "%1"=="" (
    echo [ERROR] Missing date parameter!
    echo.
    echo Usage: run_with_fix.bat [DATE]
    echo Example: run_with_fix.bat 20240901
    echo.
    pause
    exit /b 1
)

REM 设置环境变量
set SONY_DATE=%1
set SONY_DATA_DIR=data\sony\%SONY_DATE%

echo [INFO] Processing date: %SONY_DATE%
echo [INFO] Data directory: %SONY_DATA_DIR%
echo.

REM 检查必需文件
if not exist "Sony Song Import.exe" (
    echo [ERROR] Sony Song Import.exe not found!
    echo Please make sure you are in the correct directory.
    pause
    exit /b 1
)

if not exist "sony.xml" (
    echo [ERROR] Configuration file sony.xml not found!
    echo Please make sure sony.xml is in the same directory.
    pause
    exit /b 1
)

REM 创建必要目录
echo [INFO] Creating directories...
if not exist "data" mkdir data
if not exist "data\sony" mkdir data\sony
if not exist "%SONY_DATA_DIR%" (
    echo [WARNING] Data directory %SONY_DATA_DIR% does not exist.
    echo Creating empty directory...
    mkdir "%SONY_DATA_DIR%"
)
if not exist "logs" mkdir logs
if not exist "logs\%SONY_DATE%" mkdir "logs\%SONY_DATE%"

REM 检查数据文件
echo [INFO] Checking for XML files...
if exist "%SONY_DATA_DIR%\*" (
    dir "%SONY_DATA_DIR%" /b
    echo.
) else (
    echo [WARNING] No files found in %SONY_DATA_DIR%
    echo Please make sure Sony XML packages are in this directory.
    echo.
    set /p continue="Continue anyway? (y/N): "
    if /i not "%continue%"=="y" (
        echo Operation cancelled.
        pause
        exit /b 1
    )
)

REM 备份配置文件（如果需要修改）
if not exist "sony.xml.backup" (
    echo [INFO] Creating backup of sony.xml...
    copy sony.xml sony.xml.backup >nul
)

echo [INFO] Starting Sony Import...
echo [INFO] Press Ctrl+C to stop if needed
echo.
echo ========================================
echo.

REM 运行程序
"Sony Song Import.exe" %SONY_DATE%

REM 检查结果
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo [SUCCESS] Program completed successfully!
) else (
    echo.
    echo ========================================
    echo [WARNING] Program exited with code: %ERRORLEVEL%
)

echo.
echo [INFO] Check the log file for details:
if exist "logs\%SONY_DATE%\log.txt" (
    echo   logs\%SONY_DATE%\log.txt
    echo.
    echo Last 10 lines of log:
    echo ----------------------------------------
    powershell "Get-Content 'logs\%SONY_DATE%\log.txt' | Select-Object -Last 10"
    echo ----------------------------------------
) else (
    echo   [WARNING] Log file not found
)

echo.
echo ========================================
pause
