# Windows路径格式问题修复指南

## 🚨 问题描述

你遇到的错误：
```
[ERROR] Error in XML processing: 不支持给定路径的格式。
[ERROR] Main Error: 事务失败。 服务器响应为:5.6.0 Invalid content
```

## 🔧 问题原因

1. **路径格式问题**：程序在Windows环境下处理文件路径时出现格式兼容性问题
2. **邮件内容问题**：邮件发送时包含了无效字符，被邮件服务器拒绝

## ✅ 解决方案

### 方案1：临时解决方案（立即可用）

1. **禁用邮件发送**：
   编辑 `sony.xml` 文件，添加或修改：
   ```xml
   <app_setting>
       <need_email>false</need_email>
   </app_setting>
   ```

2. **检查数据目录结构**：
   确保你的目录结构正确：
   ```
   C:\Users\<USER>\Downloads\SONY_5pick_20240901\
   ├── Sony Song Import.exe
   ├── sony.xml
   ├── data\
   │   └── sony\
   │       └── 20240901\
   │           ├── [包目录1]\
   │           │   ├── *.xml
   │           │   └── resources\
   │           └── [包目录2]\
   │               ├── *.xml
   │               └── resources\
   ```

3. **使用绝对路径**：
   在命令行中使用完整路径：
   ```cmd
   cd "C:\Users\<USER>\Downloads\SONY_5pick_20240901"
   "Sony Song Import.exe" 20240901
   ```

### 方案2：配置修复

1. **检查配置文件**：
   确保 `sony.xml` 中的路径使用正确的格式：
   ```xml
   <source>
       <root_folder>data\sony\</root_folder>
       <log_folder>logs\</log_folder>
       <nas_dir>SONY\</nas_dir>
   </source>
   ```

2. **创建必要目录**：
   ```cmd
   mkdir data\sony\20240901
   mkdir logs\20240901
   ```

### 方案3：调试模式运行

1. **启用详细日志**：
   运行程序前，在命令行中设置：
   ```cmd
   set DEBUG=1
   "Sony Song Import.exe" 20240901
   ```

2. **查看详细错误**：
   检查 `logs\20240901\log.txt` 文件获取更多信息

## 🔍 故障排除步骤

### 步骤1：验证文件存在
```cmd
dir "Sony Song Import.exe"
dir sony.xml
dir data\sony\20240901
```

### 步骤2：检查XML文件
```cmd
dir data\sony\20240901\*\*.xml
```

### 步骤3：运行诊断
```cmd
echo off
echo === Sony Import Diagnostic ===
if exist "Sony Song Import.exe" (echo [OK] EXE found) else (echo [ERROR] EXE missing)
if exist "sony.xml" (echo [OK] Config found) else (echo [ERROR] Config missing)
if exist "data\sony\20240901" (echo [OK] Data dir found) else (echo [ERROR] Data dir missing)
```

## 📋 完整的运行脚本

创建 `run_with_fix.bat`：
```batch
@echo off
echo Sony Import with Windows Path Fix
echo ==================================

REM 设置代码页为UTF-8
chcp 65001 >nul

REM 检查参数
if "%1"=="" (
    echo Usage: run_with_fix.bat [DATE]
    echo Example: run_with_fix.bat 20240901
    pause
    exit /b 1
)

REM 设置环境变量
set SONY_DATE=%1
set SONY_DATA_DIR=data\sony\%SONY_DATE%

REM 检查文件
if not exist "Sony Song Import.exe" (
    echo [ERROR] Sony Song Import.exe not found
    pause
    exit /b 1
)

if not exist "sony.xml" (
    echo [ERROR] sony.xml not found
    pause
    exit /b 1
)

REM 创建目录
if not exist "data" mkdir data
if not exist "data\sony" mkdir data\sony
if not exist "%SONY_DATA_DIR%" mkdir "%SONY_DATA_DIR%"
if not exist "logs" mkdir logs
if not exist "logs\%SONY_DATE%" mkdir "logs\%SONY_DATE%"

REM 显示信息
echo [INFO] Processing date: %SONY_DATE%
echo [INFO] Data directory: %SONY_DATA_DIR%
echo [INFO] Starting Sony Import...
echo.

REM 运行程序
"Sony Song Import.exe" %SONY_DATE%

REM 显示结果
echo.
echo [INFO] Program completed. Check logs\%SONY_DATE%\log.txt for details.
pause
```

## 🎯 预期结果

修复后，你应该看到：
- 程序正常处理XML文件
- 没有路径格式错误
- 邮件发送被禁用（避免邮件错误）
- 详细的处理日志

## 📞 如果问题仍然存在

1. 将完整的日志文件内容发给我
2. 确认你的目录结构
3. 检查XML文件是否损坏
4. 验证数据库连接配置
