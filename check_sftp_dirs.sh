#!/bin/bash

echo "=== SFTP Directory Check ==="
echo "Checking SFTP server directory structure..."

# SFTP服务器信息
HOST="************"
USER="hkria"
PASSWORD="hkria"

# 创建SFTP命令脚本
cat > sftp_commands.txt << EOF
pwd
ls -la
ls -la /
ls -la /web3
ls -la /web3/SONY
ls -la /web3/SONY/20240829
quit
EOF

echo "🔌 Connecting to SFTP server..."
echo "📁 Checking directory structure..."

# 执行SFTP命令
sshpass -p "$PASSWORD" sftp -o StrictHostKeyChecking=no -b sftp_commands.txt "$USER@$HOST"

# 清理临时文件
rm -f sftp_commands.txt

echo "✅ SFTP directory check completed!"
