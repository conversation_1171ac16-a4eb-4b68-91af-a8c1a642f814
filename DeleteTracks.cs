using System;
using MySql.Data.MySqlClient;

namespace Sony_Song_Import
{
    class DeleteTracks
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== Sony Track Deletion Tool ===");

            try
            {
                // 数据库连接信息（从sony.xml获取）
                string server = "54.95.153.152";
                int port = 3336;
                string database = "avs";
                string user = "avs";
                string password = "NCPxzZafXFRtPY4t";

                Console.WriteLine($"Database: {server}:{port}/{database}");
                Console.WriteLine($"User: {user}");
                Console.WriteLine();

                // 要删除的track_id列表
                int[] trackIds = {633034, 4250186, 4250187, 4250188, 4250189, 4250190, 4250191, 4250192, 4250193, 4250194, 4250195, 4250196, 4250197};

                Console.WriteLine("Track IDs to delete:");
                foreach (int id in trackIds)
                {
                    Console.WriteLine($"  - {id}");
                }
                Console.WriteLine();

                // 创建数据库连接
                string connectionString = $"Server={server};Port={port};Database={database};Uid={user};Pwd={password};";
                
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    Console.WriteLine("🔌 Connecting to database...");
                    connection.Open();
                    Console.WriteLine("✅ Database connected successfully");
                    
                    // 构建IN子句
                    string inClause = string.Join(",", trackIds);
                    
                    // 执行删除操作
                    Console.WriteLine("🗑️ Starting deletion process...");
                    
                    // 1. 删除 label_link
                    Console.WriteLine("Deleting from label_link...");
                    string sql1 = $"DELETE FROM `avs`.`label_link` WHERE `track_id` IN ({inClause})";
                    using (MySqlCommand cmd = new MySqlCommand(sql1, connection))
                    {
                        int affected = cmd.ExecuteNonQuery();
                        Console.WriteLine($"  ✅ Deleted {affected} records from label_link");
                    }
                    
                    // 2. 删除 m3_album_link
                    Console.WriteLine("Deleting from m3_album_link...");
                    string sql2 = $"DELETE FROM `avs`.`m3_album_link` WHERE `track_id` IN ({inClause})";
                    using (MySqlCommand cmd = new MySqlCommand(sql2, connection))
                    {
                        int affected = cmd.ExecuteNonQuery();
                        Console.WriteLine($"  ✅ Deleted {affected} records from m3_album_link");
                    }
                    
                    // 3. 删除 m3_artist_link
                    Console.WriteLine("Deleting from m3_artist_link...");
                    string sql3 = $"DELETE FROM `avs`.`m3_artist_link` WHERE `track_id` IN ({inClause})";
                    using (MySqlCommand cmd = new MySqlCommand(sql3, connection))
                    {
                        int affected = cmd.ExecuteNonQuery();
                        Console.WriteLine($"  ✅ Deleted {affected} records from m3_artist_link");
                    }
                    
                    // 4. 删除 track_name
                    Console.WriteLine("Deleting from track_name...");
                    string sql4 = $"DELETE FROM `avs`.`track_name` WHERE `track_id` IN ({inClause})";
                    using (MySqlCommand cmd = new MySqlCommand(sql4, connection))
                    {
                        int affected = cmd.ExecuteNonQuery();
                        Console.WriteLine($"  ✅ Deleted {affected} records from track_name");
                    }
                    
                    // 5. 删除 track (主表)
                    Console.WriteLine("Deleting from track...");
                    string sql5 = $"DELETE FROM `avs`.`track` WHERE `track_id` IN ({inClause})";
                    using (MySqlCommand cmd = new MySqlCommand(sql5, connection))
                    {
                        int affected = cmd.ExecuteNonQuery();
                        Console.WriteLine($"  ✅ Deleted {affected} records from track");
                    }
                    
                    // 验证删除结果
                    Console.WriteLine("🔍 Verifying deletion...");
                    string verifySQL = $"SELECT COUNT(*) FROM `avs`.`track` WHERE `track_id` IN ({inClause})";
                    using (MySqlCommand cmd = new MySqlCommand(verifySQL, connection))
                    {
                        int remaining = Convert.ToInt32(cmd.ExecuteScalar());
                        if (remaining == 0)
                        {
                            Console.WriteLine("🎉 ✅ All tracks deleted successfully!");
                        }
                        else
                        {
                            Console.WriteLine($"⚠️ Warning: {remaining} tracks still remain");
                        }
                    }
                }
                
                Console.WriteLine();
                Console.WriteLine("🚀 Database cleanup completed!");
                Console.WriteLine("Ready for SFTP upload test...");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            
            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
