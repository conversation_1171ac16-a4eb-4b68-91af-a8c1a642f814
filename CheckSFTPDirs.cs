using System;
using System.IO;
using Renci.SshNet;

namespace Sony_Song_Import
{
    class CheckSFTPDirs
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== SFTP Directory Structure Check ===");
            
            try
            {
                // SFTP连接信息
                string server = "192.168.2.53";
                int port = 22;
                string username = "hkria";
                string password = "1";
                
                Console.WriteLine($"Connecting to {server}:{port} as {username}...");
                
                var connectionInfo = new PasswordConnectionInfo(server, port, username, password);
                connectionInfo.Timeout = TimeSpan.FromSeconds(30);
                
                using (var sftp = new SftpClient(connectionInfo))
                {
                    sftp.Connect();
                    
                    if (sftp.IsConnected)
                    {
                        Console.WriteLine("✅ Connected successfully!");
                        Console.WriteLine();
                        
                        // 检查当前工作目录
                        Console.WriteLine("📁 Current working directory:");
                        string currentDir = sftp.WorkingDirectory;
                        Console.WriteLine($"   {currentDir}");
                        Console.WriteLine();
                        
                        // 检查根目录
                        Console.WriteLine("📁 Root directory (/) contents:");
                        try
                        {
                            var rootFiles = sftp.ListDirectory("/");
                            foreach (var file in rootFiles)
                            {
                                string type = file.IsDirectory ? "[DIR]" : "[FILE]";
                                Console.WriteLine($"   {type} {file.Name}");
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"   ❌ Cannot access /: {ex.Message}");
                        }
                        Console.WriteLine();
                        
                        // 检查web3目录
                        Console.WriteLine("📁 Checking /web3 directory:");
                        try
                        {
                            if (sftp.Exists("/web3"))
                            {
                                Console.WriteLine("   ✅ /web3 exists!");
                                var web3Files = sftp.ListDirectory("/web3");
                                foreach (var file in web3Files)
                                {
                                    string type = file.IsDirectory ? "[DIR]" : "[FILE]";
                                    Console.WriteLine($"   {type} {file.Name}");
                                }
                            }
                            else
                            {
                                Console.WriteLine("   ❌ /web3 does not exist");
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"   ❌ Cannot access /web3: {ex.Message}");
                        }
                        Console.WriteLine();
                        
                        // 检查SONY目录
                        Console.WriteLine("📁 Checking /web3/SONY directory:");
                        try
                        {
                            if (sftp.Exists("/web3/SONY"))
                            {
                                Console.WriteLine("   ✅ /web3/SONY exists!");
                                var sonyFiles = sftp.ListDirectory("/web3/SONY");
                                foreach (var file in sonyFiles)
                                {
                                    string type = file.IsDirectory ? "[DIR]" : "[FILE]";
                                    Console.WriteLine($"   {type} {file.Name}");
                                }
                            }
                            else
                            {
                                Console.WriteLine("   ❌ /web3/SONY does not exist");
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"   ❌ Cannot access /web3/SONY: {ex.Message}");
                        }
                        Console.WriteLine();
                        
                        // 检查今天的目录
                        Console.WriteLine("📁 Checking /web3/SONY/20240829 directory:");
                        try
                        {
                            if (sftp.Exists("/web3/SONY/20240829"))
                            {
                                Console.WriteLine("   ✅ /web3/SONY/20240829 exists!");
                                var todayFiles = sftp.ListDirectory("/web3/SONY/20240829");
                                foreach (var file in todayFiles)
                                {
                                    string type = file.IsDirectory ? "[DIR]" : "[FILE]";
                                    Console.WriteLine($"   {type} {file.Name}");
                                }
                            }
                            else
                            {
                                Console.WriteLine("   ❌ /web3/SONY/20240829 does not exist");
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"   ❌ Cannot access /web3/SONY/20240829: {ex.Message}");
                        }
                        
                        sftp.Disconnect();
                        Console.WriteLine();
                        Console.WriteLine("🔍 Directory check completed!");
                    }
                    else
                    {
                        Console.WriteLine("❌ Failed to connect!");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            
            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
