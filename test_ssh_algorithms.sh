#!/bin/bash

# SSH算法测试脚本
SERVER="192.168.2.53"
PORT="2222"
USER="hkria"

echo "=== SSH服务器算法支持测试 ==="
echo "服务器: $SERVER:$PORT"
echo "用户: $USER"
echo ""

# 测试基础连接
echo "1. 测试基础连接..."
timeout 10 ssh -o ConnectTimeout=5 -o BatchMode=yes -p $PORT $USER@$SERVER "echo 'SSH连接成功'" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ 基础SSH连接成功"
else
    echo "❌ 基础SSH连接失败"
fi
echo ""

# 测试SSH.NET 2016支持的密钥交换算法
echo "2. 测试密钥交换算法..."
KEX_ALGORITHMS=(
    "diffie-hellman-group1-sha1"
    "diffie-hellman-group14-sha1"
    "diffie-hellman-group-exchange-sha1"
    "diffie-hellman-group-exchange-sha256"
)

for kex in "${KEX_ALGORITHMS[@]}"; do
    echo -n "测试 $kex: "
    timeout 10 ssh -o ConnectTimeout=5 -o BatchMode=yes -o KexAlgorithms=$kex -p $PORT $USER@$SERVER "echo 'OK'" 2>/dev/null
    if [ $? -eq 0 ]; then
        echo "✅ 支持"
    else
        echo "❌ 不支持"
    fi
done
echo ""

# 测试主机密钥算法
echo "3. 测试主机密钥算法..."
HOST_KEY_ALGORITHMS=(
    "ssh-rsa"
    "ssh-dss"
)

for hka in "${HOST_KEY_ALGORITHMS[@]}"; do
    echo -n "测试 $hka: "
    timeout 10 ssh -o ConnectTimeout=5 -o BatchMode=yes -o HostKeyAlgorithms=$hka -p $PORT $USER@$SERVER "echo 'OK'" 2>/dev/null
    if [ $? -eq 0 ]; then
        echo "✅ 支持"
    else
        echo "❌ 不支持"
    fi
done
echo ""

# 测试加密算法
echo "4. 测试加密算法..."
CIPHERS=(
    "aes128-cbc"
    "aes128-ctr"
    "3des-cbc"
)

for cipher in "${CIPHERS[@]}"; do
    echo -n "测试 $cipher: "
    timeout 10 ssh -o ConnectTimeout=5 -o BatchMode=yes -o Ciphers=$cipher -p $PORT $USER@$SERVER "echo 'OK'" 2>/dev/null
    if [ $? -eq 0 ]; then
        echo "✅ 支持"
    else
        echo "❌ 不支持"
    fi
done
echo ""

# 测试MAC算法
echo "5. 测试MAC算法..."
MACS=(
    "hmac-sha1"
    "hmac-md5"
    "hmac-sha2-256"
)

for mac in "${MACS[@]}"; do
    echo -n "测试 $mac: "
    timeout 10 ssh -o ConnectTimeout=5 -o BatchMode=yes -o MACs=$mac -p $PORT $USER@$SERVER "echo 'OK'" 2>/dev/null
    if [ $? -eq 0 ]; then
        echo "✅ 支持"
    else
        echo "❌ 不支持"
    fi
done
echo ""

# 测试SSH.NET 2016兼容的组合
echo "6. 测试SSH.NET 2016兼容组合..."
echo -n "测试完整兼容组合: "
timeout 10 ssh -o ConnectTimeout=5 -o BatchMode=yes \
    -o KexAlgorithms=diffie-hellman-group1-sha1 \
    -o HostKeyAlgorithms=ssh-rsa \
    -o Ciphers=aes128-cbc \
    -o MACs=hmac-sha1 \
    -p $PORT $USER@$SERVER "echo 'SSH.NET 2016兼容测试成功'" 2>/dev/null

if [ $? -eq 0 ]; then
    echo "✅ SSH.NET 2016完全兼容"
else
    echo "❌ SSH.NET 2016不兼容"
fi

echo ""
echo "=== 测试完成 ==="
