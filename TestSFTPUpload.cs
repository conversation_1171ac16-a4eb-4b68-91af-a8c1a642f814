using System;
using System.IO;
using Sony_Song_Import.util;

namespace Sony_Song_Import
{
    class TestSFTPUpload
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== Sony SFTP Upload Test ===");
            
            try
            {
                // 读取配置
                AppCommon.readConfig("sony.xml");
                
                Console.WriteLine($"FTP Server: {AppCommon.FTPServer}");
                Console.WriteLine($"FTP User: {AppCommon.FTPUser}");
                Console.WriteLine();
                
                // 查找一个实际的音频文件进行测试
                string testAudioFile = "data/sony/20240829/N_A10301A0004750949S_20240829191151688/resources/A10301A0004750949S_70045247.20122.mp3";
                
                if (!File.Exists(testAudioFile))
                {
                    Console.WriteLine($"❌ Test audio file not found: {testAudioFile}");
                    
                    // 尝试查找任何MP3文件
                    string resourceDir = "data/sony/20240829/N_A10301A0004750949S_20240829191151688/resources/";
                    if (Directory.Exists(resourceDir))
                    {
                        string[] mp3Files = Directory.GetFiles(resourceDir, "*.mp3");
                        if (mp3Files.Length > 0)
                        {
                            testAudioFile = mp3Files[0];
                            Console.WriteLine($"✅ Using alternative file: {testAudioFile}");
                        }
                        else
                        {
                            Console.WriteLine("❌ No MP3 files found in resources directory");
                            return;
                        }
                    }
                    else
                    {
                        Console.WriteLine("❌ Resources directory not found");
                        return;
                    }
                }
                
                Console.WriteLine($"📁 Test file: {testAudioFile}");
                Console.WriteLine($"📊 File size: {new FileInfo(testAudioFile).Length} bytes");
                Console.WriteLine();
                
                // 测试文件加密
                Console.WriteLine("🔐 Testing file encryption...");
                string encryptedFile = testAudioFile + ".encrypted";
                
                if (Util.encryptFile(testAudioFile, encryptedFile))
                {
                    Console.WriteLine("✅ File encryption successful");
                    Console.WriteLine($"📁 Encrypted file: {encryptedFile}");
                    Console.WriteLine($"📊 Encrypted size: {new FileInfo(encryptedFile).Length} bytes");
                }
                else
                {
                    Console.WriteLine("❌ File encryption failed");
                    return;
                }
                
                Console.WriteLine();
                
                // 测试SFTP上传
                Console.WriteLine("📤 Testing SFTP upload...");
                string remotePath = "SONY/test/" + Path.GetFileName(encryptedFile);
                
                Console.WriteLine($"🎯 Remote path: {remotePath}");
                Console.WriteLine("🔄 Starting upload...");
                
                bool uploadResult = Util.uploadFile(encryptedFile, remotePath);
                
                if (uploadResult)
                {
                    Console.WriteLine("🎉 ✅ SFTP Upload Test: SUCCESS!");
                    Console.WriteLine("🚀 Sony音乐导入系统的SFTP上传功能完全正常！");
                }
                else
                {
                    Console.WriteLine("❌ SFTP Upload Test: FAILED");
                    Console.WriteLine("请检查SFTP服务器连接和配置");
                }
                
                // 清理测试文件
                if (File.Exists(encryptedFile))
                {
                    File.Delete(encryptedFile);
                    Console.WriteLine("🧹 Cleaned up encrypted test file");
                }
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            
            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
