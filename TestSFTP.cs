using System;
using System.IO;
using Renci.SshNet;
using Sony_Song_Import.util;

namespace Sony_Song_Import
{
    class TestSFTP
    {
        static void Main(string[] args)
        {
            Console.WriteLine("Testing SFTP Connection...");
            
            // 读取配置
            AppCommon.FTPServer = "192.168.2.53";
            AppCommon.FTPUser = "hkria";
            AppCommon.FTPPassword = "1";
            
            // 测试连接
            bool result = Util.uploadFile("test_upload.txt", "test/test_upload.txt");
            
            if (result)
            {
                Console.WriteLine("SFTP Upload Test: SUCCESS");
            }
            else
            {
                Console.WriteLine("SFTP Upload Test: FAILED");
            }
            
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
